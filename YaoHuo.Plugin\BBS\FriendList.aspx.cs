﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using KeLin.ClassManager.ExUtility;
using System;
using System.Collections.Generic;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.Template.Models;
using YaoHuo.Plugin.BBS.Models;
using System.Data.SqlClient;

namespace YaoHuo.Plugin.BBS
{
    public class FriendList : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string KL_ADDFriendCount = PubConstant.GetAppString("KL_ADDFriendCount");

        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        public string KelinkWAP_Check = PubConstant.GetConnectionString("kelinkWAP_Check");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string friendtype = "";

        public string backurl = "";

        public string linkTOP = "";

        public string INFO = "";

        public List<wap_friends_Model> listVo = null;

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            this.action = base.GetRequestValue("action");
            this.backurl = base.Request.QueryString.Get("backurl");
            if (this.backurl == null || this.backurl == "")
            {
                this.backurl = base.Request.Form.Get("backurl");
            }
            if (this.backurl == null || this.backurl == "")
            {
                this.backurl = "myfile.aspx?siteid=" + base.siteid;
            }
            this.backurl = base.ToHtm(this.backurl);
            this.backurl = HttpUtility.UrlDecode(this.backurl);
            this.backurl = WapTool.URLtoWAP(this.backurl);
            this.friendtype = base.GetRequestValue("friendtype");
            if (!WapTool.IsNumeric(this.friendtype))
            {
                this.friendtype = "0";
            }

            // 屏蔽 friendtype 2, 4, 5，只允许 0 和 1
            if (WapTool.IsNumeric(this.friendtype)) // 确保是数字类型再进行判断
            {
                int type = int.Parse(this.friendtype);
                if (type != 0 && type != 1)
                {
                    this.friendtype = "0"; // 重设为默认的好友列表
                }
            }

            base.IsLogin(base.userid, this.backurl);

            try
            {
                // 检查用户UI偏好并处理版本切换
                bool newVersionRendered = CheckAndHandleUIPreference();
                if (newVersionRendered)
                {
                    // 新版渲染成功，直接返回，不再执行后续的旧版代码
                    return;
                }
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 Response.End() 的正常行为，直接重新抛出
                System.Diagnostics.Debug.WriteLine("FriendList: Page_Load 收到 ThreadAbortException，新版渲染成功");
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FriendList: Page_Load 异常: {ex.ToString()}");
                ERROR = WapTool.ErrorToString(ex.ToString());
            }

            switch (this.action)
            {
                case "addfriend":
                    this.goaddfriend();
                    break;

                case "class":
                    this.LoadFriendData();
                    break;

                default:
                    this.LoadFriendData();
                    break;
            }
        }

        /// <summary>
        /// 检查用户UI偏好并处理版本切换
        /// </summary>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = "";
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }
            if (string.IsNullOrEmpty(uiPreference))
            {
                uiPreference = "old";
            }

            System.Diagnostics.Debug.WriteLine($"FriendList: UI偏好={uiPreference}");

            if (uiPreference == "new")
            {
                return TryRenderWithHandlebars();
            }
            return false;
        }

        /// <summary>
        /// 尝试使用Handlebars模板渲染页面
        /// </summary>
        private bool TryRenderWithHandlebars()
        {
            try
            {
                // 使用反射检查TemplateService可用性
                var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");
                
                if (templateServiceType != null)
                {
                    var getViewModeMethod = templateServiceType.GetMethod("GetViewMode");
                    var renderPageMethod = templateServiceType.GetMethod("RenderPageWithLayout");

                    if (getViewModeMethod != null && renderPageMethod != null)
                    {
                        string viewMode = (string)getViewModeMethod.Invoke(null, null);
                        
                        if (viewMode == "new")
                        {
                            System.Diagnostics.Debug.WriteLine("FriendList: 开始新版渲染");
                            RenderWithHandlebars();
                            // 如果代码执行到这里，说明 Response.End() 没有被调用
                            return true;
                        }
                    }
                }

                ERROR = "Handlebars模板服务不可用";
                System.Diagnostics.Debug.WriteLine("FriendList: Handlebars模板服务不可用");
                return false;
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 Response.End() 的正常行为
                // 不调用 Thread.ResetAbort()，让异常自然传播，确保请求真正终止
                System.Diagnostics.Debug.WriteLine("FriendList: 新版渲染成功，ThreadAbortException 正常传播");
                throw; // 重新抛出异常，让它传播到上层
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FriendList: TryRenderWithHandlebars 异常: {ex.ToString()}");
                ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 使用Handlebars模板渲染页面
        /// </summary>
        private void RenderWithHandlebars()
        {
            try
            {
                // 在新版渲染中直接处理数据加载，不调用旧版方法
                switch (this.action)
                {
                    case "addfriend":
                        this.goaddfriend();
                        // 添加好友后，需要重新加载数据用于显示
                        this.LoadDataForNewUI();
                        break;
                    case "class":
                    default:
                        this.LoadDataForNewUI();
                        break;
                }

                // 构建页面数据模型
                var pageModel = BuildFriendListPageModel();

                // 获取页面标题
                string pageTitle = GetPageTitle();
                System.Diagnostics.Debug.WriteLine($"FriendList: 渲染页面 - {pageTitle}，数据量: {pageModel.FriendsList.Count}");

                // 调用 TemplateService.RenderPageWithLayout 方法
                var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");
                if (templateServiceType == null)
                {
                    throw new InvalidOperationException("无法获取 TemplateService 类型");
                }
                
                var renderMethod = templateServiceType.GetMethod("RenderPageWithLayout");
                if (renderMethod == null)
                {
                    throw new InvalidOperationException("无法获取 RenderPageWithLayout 方法");
                }

                // 创建 HeaderOptionsModel - 修复：使用正确的类型名称
                var headerOptions = new HeaderOptionsModel { ShowViewModeToggle = false };

                // 在黑名单页面添加问号图标
                if (this.friendtype == "1")
                {
                    headerOptions.CustomButtons.Add(new HeaderButtonModel
                    {
                        Id = "blacklist-help",
                        Icon = "help-circle",
                        OnClick = "showBlacklistHelp()",
                        Tooltip = "黑名单帮助"
                    });
                }

                string finalHtml = (string)renderMethod.Invoke(null, new object[]
                {
                    "~/Template/Pages/FriendList.hbs",
                    pageModel,
                    pageTitle,
                    headerOptions,
                    null,
                    "~/Template/Layouts/MainLayout.hbs"
                });

                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End();
            }
            catch (System.Threading.ThreadAbortException)
            {
                // Response.End() 的正常行为，不需要重新抛出
                System.Diagnostics.Debug.WriteLine("FriendList: Response.End() 正常终止");
                // 注意：不调用 Thread.ResetAbort()，让异常自然传播到上层处理
            }
            catch (Exception ex)
            {
                // 错误处理
                System.Diagnostics.Debug.WriteLine($"FriendList: RenderWithHandlebars 严重错误: {ex.ToString()}");
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red; padding:20px; border:2px solid red;'>页面渲染时发生严重错误: {ex.Message}<br><br>详细信息:<br><pre>{ex.ToString()}</pre></div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest();
            }
        }

        /// <summary>
        /// 统一的数据加载方法（支持新旧版本UI）
        /// </summary>
        private void LoadDataForNewUI()
        {
            this.key = base.GetRequestValue("key");
            
            try
            {
                this.pageSize = Convert.ToInt32(base.siteVo.MaxPerPage_Default);
                
                if (base.GetRequestValue("page") != "")
                {
                    this.CurrentPage = long.Parse(base.GetRequestValue("page"));
                }
                
                // 使用 FriendQueryService 进行安全的参数化查询
                var friendQueryService = new YaoHuo.Plugin.WebSite.Services.FriendQueryService(this.KelinkWAP_Check);
                
                YaoHuo.Plugin.WebSite.Services.FriendQueryResult queryResult;
                
                if (this.friendtype == "4")
                {
                    // 查询"追求我的人"列表
                    queryResult = friendQueryService.SearchWhoLovesMe(
                        base.siteid, 
                        base.userid, 
                        this.key, 
                        (int)this.pageSize, 
                        (int)this.CurrentPage);
                }
                else
                {
                    // 查询好友/黑名单/追求列表
                    queryResult = friendQueryService.SearchFriends(
                        base.siteid, 
                        base.userid, 
                        this.friendtype, 
                        this.key, 
                        (int)this.pageSize, 
                        (int)this.CurrentPage);
                }
                
                // 检查查询结果
                if (queryResult.HasError)
                {
                    this.ERROR = queryResult.ErrorMessage;
                    System.Diagnostics.Debug.WriteLine($"FriendList: 查询出错 - {queryResult.ErrorMessage}");
                    return;
                }
                
                // 设置查询结果
                this.listVo = queryResult.Friends;
                this.total = queryResult.Total;
                this.CurrentPage = queryResult.CurrentPage;
                
                // 构建链接URL（保持与原版兼容）
                this.linkURL = base.http_start + "bbs/FriendList.aspx?action=class&amp;siteid=" + base.siteid + "&amp;classid=" + base.classid + "&amp;friendtype=" + this.friendtype + "&amp;key=" + HttpUtility.UrlEncode(this.key) + "&amp;backurl=" + HttpUtility.UrlEncode(this.backurl) + "&amp;getTotal=" + this.total;
                this.linkTOP = WapTool.GetPageLinkShowTOP(base.ver, base.lang, this.total, this.pageSize, this.CurrentPage, this.linkURL);
                this.linkURL = WapTool.GetPageLink(base.ver, base.lang, Convert.ToInt32(this.total), this.pageSize, this.CurrentPage, this.linkURL);
                
                System.Diagnostics.Debug.WriteLine($"FriendList: LoadDataForNewUI 完成 - 总数:{this.total}, 当前页:{this.CurrentPage}, 数据量:{this.listVo.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FriendList: LoadDataForNewUI 异常 - {ex.ToString()}");
                this.ERROR = ex.ToString();
            }
        }

        /// <summary>
        /// 兼容旧版UI的数据加载方法（统一使用安全的查询服务）
        /// </summary>
        private void LoadFriendData()
        {
            // 直接调用新版的数据加载方法，确保安全性
            this.LoadDataForNewUI();
        }

        /// <summary>
        /// 构建FriendList页面数据模型
        /// </summary>
        private FriendListPageModel BuildFriendListPageModel()
        {
            var model = new FriendListPageModel
            {
                PageTitle = GetPageTitle(),
                FriendType = this.friendtype,
                SearchKey = this.key ?? "",
                ShowSearchBox = this.friendtype == "0",
                Error = this.ERROR ?? "",
                Info = this.INFO ?? "",
                BackUrl = this.backurl ?? "",
                UserId = base.userid ?? "",
                SiteId = base.siteid ?? "",
                ClassId = base.classid ?? "",
                HasOperations = this.friendtype == "1" // 只在黑名单页面显示清空按钮
            };

            // 添加 user_BLL 实例用于查询用户信息
            user_BLL userBll = new user_BLL(this.a);

            // 构建好友列表
            if (this.listVo != null)
            {
                foreach (var friend in this.listVo)
                {
                    var friendItem = new FriendItemModel
                    {
                        Id = friend.id,
                        FriendUserId = friend.frienduserid.ToString(),
                        FriendUserName = friend.friendusername ?? "",
                        FriendNickname = friend.friendnickname ?? "未知用户",
                        FriendType = (int)friend.friendtype,
                        AddTime = friend.addtime,
                        AvatarUrl = "", // 占位符，暂未实现
                        IsOnline = false, // <-- 将在这里根据 user_Model 设置
                        PrivateMessageUrl = "", // 占位符，暂未实现
                        DeleteUrl = $"?action=del&siteid={base.siteid}&touserid={friend.frienduserid}&friendtype={this.friendtype}&backurl={HttpUtility.UrlEncode(this.backurl)}",
                        UserDetailUrl = $"../bbs/userinfo.aspx?siteid={base.siteid}&touserid={friend.frienduserid}"
                    };

                    // 根据 frienduserid 查询对应的 user_Model
                    user_Model friendUser = userBll.getUserInfo(friend.frienduserid.ToString(), base.siteid);

                    // 如果找到了 user_Model，设置 IsOnline 和 AvatarUrl
                    if (friendUser != null)
                    {
                        // 根据 isonline 属性设置 IsOnline (假设 "1" 为在线)
                        friendItem.IsOnline = (friendUser.isonline == "1");
                        // 构建完整的头像 URL，使用正确的路径处理逻辑
                        friendItem.AvatarUrl = GetCorrectAvatarUrl(friendUser.headimg);
                    }
                    else
                    {
                         // 如果找不到用户（例如用户已被删除），默认为离线
                         friendItem.IsOnline = false;
                    }

                    model.FriendsList.Add(friendItem);
                }
            }

            // 处理分页信息
            var paginationModel = new PaginationModel();
            if (this.linkURL != null)
            {
                // 从现有的分页数据中提取信息
                paginationModel.CurrentPage = (int)this.CurrentPage;
                paginationModel.Total = (int)this.total;
                paginationModel.PageSize = (int)this.pageSize;
                paginationModel.TotalPages = paginationModel.PageSize > 0 ? 
                    (int)Math.Ceiling((double)paginationModel.Total / paginationModel.PageSize) : 0;
                
                paginationModel.ShowPagination = paginationModel.Total > paginationModel.PageSize;
                paginationModel.IsFirstPage = paginationModel.CurrentPage <= 1;
                paginationModel.IsLastPage = paginationModel.CurrentPage >= paginationModel.TotalPages;
            }
            else
            {
                paginationModel.ShowPagination = false;
                paginationModel.CurrentPage = 1;
                paginationModel.TotalPages = 1;
                paginationModel.Total = model.FriendsList.Count;
                paginationModel.PageSize = model.FriendsList.Count;
                paginationModel.IsFirstPage = true;
                paginationModel.IsLastPage = true;
            }

            model.Pagination = paginationModel;

            return model;
        }

        /// <summary>
        /// 获取页面标题
        /// </summary>
        private string GetPageTitle()
        {
            switch (this.friendtype)
            {
                case "0":
                    return "我的好友";
                case "1":
                    return "我的黑名单";
                case "2":
                    return "我的追求";
                case "4":
                    return "追求我的人";
                case "5":
                    return "推荐好友";
                default:
                    return "好友列表";
            }
        }

        public void goaddfriend()
        {
            string requestValue = base.GetRequestValue("touserid");
            user_Model user_Model = new user_BLL(this.a).getUserInfo(requestValue, base.siteid);
            if (!WapTool.IsNumeric(this.KL_ADDFriendCount))
            {
                this.KL_ADDFriendCount = "10";
            }
            long listCount;
            listCount = new wap_friends_BLL(this.a).GetListCount(" (DATEDIFF(dd, addtime, GETDATE()) < 1) and friendtype=0 and siteid=" + long.Parse(base.siteid) + " and userid=" + long.Parse(base.userid));
            if (user_Model == null)
            {
                this.INFO = "NOTUSER";
            }
            else if (listCount > long.Parse(this.KL_ADDFriendCount))
            {
                this.INFO = "MAX";
            }
            else if (base.userid == requestValue)
            {
                this.INFO = "MY";
            }
            else if (WapTool.IsLockuser(base.siteid, base.userid, "0") > -1L)
            {
                this.INFO = "LOCK";
            }
            else if (WapTool.IsExistFriend(base.siteid, base.userid, requestValue, this.friendtype))
            {
                this.INFO = "HASEXIST";
            }
            else
            {
                string title = "";
                if (this.friendtype == "0")
                {
                    title = "TA的好友|TA的好友|his friend";
                }
                else if (this.friendtype == "1")
                {
                    title = "TA的黑名单|TA的黑名单|black";
                }
                else if (this.friendtype == "2")
                {
                    title = "TA的追求|TA的追求|his love";
                }
                string text = user_Model.nickname;
                //限制黑名单上限
                if (friendtype == "1")
                {
                    //查询用户信息
                    var userInfo = MainBll.getUserInfo(userid, siteid);
                    var isResult = UserBlockingService.AddBlackUser(userInfo, KelinkWAP_Check, requestValue);
                    if (!string.IsNullOrEmpty(isResult))
                    {
                        INFO = isResult;
                        // 检查是否处于新版UI渲染模式，如果不是才调用数据加载
                        if (!IsNewUIMode())
                        {
                            this.LoadFriendData();
                        }
                        return;
                    }
                }
                
                try
                {
                    // 使用参数化查询进行安全的数据库操作
                    string insertSql = "INSERT INTO wap_friends(siteid,userid,frienduserid,friendusername,friendnickname,friendtype) VALUES(@siteid,@userid,@frienduserid,@friendusername,@friendnickname,@friendtype)";
                    
                    SqlParameter[] parameters = {
                        new SqlParameter("@siteid", base.siteid),
                        new SqlParameter("@userid", base.userid),
                        new SqlParameter("@frienduserid", requestValue),
                        new SqlParameter("@friendusername", ""), // 通常为空
                        new SqlParameter("@friendnickname", text ?? ""),
                        new SqlParameter("@friendtype", this.friendtype)
                    };
                    
                    int result = DbHelperSQL.ExecuteNonQuery(this.KelinkWAP_Check, System.Data.CommandType.Text, insertSql, parameters);
                    System.Diagnostics.Debug.WriteLine($"FriendList: 插入好友记录结果: {result}");
                    
                    if (this.friendtype != "1")
                {
                    string text2 = base.nickname + base.GetLang("将你加入|将你加入|to") + base.GetLang(title);
                    string text3 = base.GetLang("操作时间|操作时间|Operation Time") + ":" + DateTime.Now;
                        
                        // 消息插入也使用参数化查询
                        string messageSql = "INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem) VALUES(@siteid,@userid,@nickname,@title,@content,@touserid,@issystem)";
                        
                        SqlParameter[] messageParameters = {
                            new SqlParameter("@siteid", base.siteid),
                            new SqlParameter("@userid", base.userid),
                            new SqlParameter("@nickname", base.nickname ?? ""),
                            new SqlParameter("@title", text2 ?? ""),
                            new SqlParameter("@content", text3 ?? ""),
                            new SqlParameter("@touserid", requestValue),
                            new SqlParameter("@issystem", 1)
                        };
                        
                        int messageResult = DbHelperSQL.ExecuteNonQuery(this.KelinkWAP_Check, System.Data.CommandType.Text, messageSql, messageParameters);
                        System.Diagnostics.Debug.WriteLine($"FriendList: 插入消息记录结果: {messageResult}");
                    }
                    
                    // 根据操作类型设置不同的成功信息
                    if (this.friendtype == "0")
                    {
                        this.INFO = "OK_FRIEND"; // 添加好友成功
                    }
                    else if (this.friendtype == "1")
                    {
                        this.INFO = "OK_BLACKLIST"; // 加入黑名单成功
                    }
                    else if (this.friendtype == "2")
                    {
                        this.INFO = "OK_LOVE"; // 添加追求成功
                    }
                    else
                    {
                        this.INFO = "OK"; // 其他操作成功
                    }
                    base.Action_user_doit(6);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"FriendList: 添加好友时发生异常: {ex.ToString()}");
                    this.ERROR = "添加好友失败: " + ex.Message;
                }
            }
            
            // 检查是否处于新版UI渲染模式，如果不是才调用数据加载
            if (!IsNewUIMode())
            {
                this.LoadFriendData();
            }
        }

        /// <summary>
        /// 检查当前是否处于新版UI模式
        /// </summary>
        private bool IsNewUIMode()
        {
            string uiPreference = "";
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }
            return uiPreference == "new";
        }

        /// <summary>
        /// 获取正确的头像URL
        /// 参考WebTool.cs中GetHeadImgURL方法的逻辑
        /// </summary>
        /// <param name="headimg">头像路径</param>
        /// <returns>完整的头像URL</returns>
        private string GetCorrectAvatarUrl(string headimg)
        {
            // 如果头像为空，使用默认头像
            if (string.IsNullOrEmpty(headimg))
            {
                return base.http_start + "bbs/head/64.gif";
            }

            // 如果是外部链接（以http开头），直接返回
            if (headimg.StartsWith("http://") || headimg.StartsWith("https://"))
            {
                return headimg;
            }

            // 如果包含斜杠，说明是自定义路径（如相册图片），直接拼接站点URL
            if (headimg.IndexOf("/") >= 0)
            {
                return base.http_start + headimg;
            }

            // 否则是系统头像，拼接系统头像路径
            return base.http_start + "bbs/head/" + headimg;
        }
    }
}