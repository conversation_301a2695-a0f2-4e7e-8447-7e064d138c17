<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值与兑换</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        /* CSS自定义属性 (Design Tokens) */
        :root {
            /* 主色系 */
            --color-primary: #58b4b0;
            --color-primary-dark: #4a9c98;
            --color-primary-light: #7cd0cb;
            --color-primary-alpha-10: rgba(88, 180, 176, 0.1);
            --color-primary-alpha-30: rgba(88, 180, 176, 0.3);
            --color-primary-alpha-05: rgba(88, 180, 176, 0.05);
            
            /* 文本颜色 */
            --color-text-primary: #1f2937;
            --color-text-secondary: #6b7280;
            --color-text-tertiary: #4b5563;
            --color-text-light: #9ca3af;
            --color-text-white: #ffffff;
            
            /* 状态颜色 */
            --color-success: #10b981;
            --color-danger: #dc2626;
            --color-warning: #d97706;
            --color-info: #3b82f6;
            
            /* 背景颜色 */
            --color-bg-primary: #f9fafb;
            --color-bg-white: #ffffff;
            --color-bg-gray-50: #f9fafb;
            --color-bg-gray-100: #f3f4f6;
            --color-bg-blue-50: #eff6ff;
            
            /* 边框颜色 */
            --color-border-light: #f3f4f6;
            --color-border-normal: #e5e7eb;
            --color-border-dark: #d1d5db;
            
            /* 间距系统 */
            --spacing-1: 0.25rem;
            --spacing-2: 0.5rem;
            --spacing-3: 0.75rem;
            --spacing-4: 1rem;
            --spacing-5: 1.25rem;
            --spacing-6: 1.5rem;
            --spacing-8: 2rem;
            --spacing-10: 2.5rem;
            --spacing-12: 3rem;
            --spacing-16: 4rem;
            
            /* 圆角 */
            --radius-sm: 0.25rem;
            --radius-base: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;
            --radius-circle: 50%;
            
            /* 阴影 */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            
            /* 过渡时间 */
            --transition-base: 0.2s;
            --transition-fast: 0.15s;
            
            /* 图标尺寸 */
            --icon-sm: 1rem;
            --icon-base: 1.25rem;
            --icon-lg: 1.5rem;
            --icon-xl: 2rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--color-bg-primary);
            line-height: 1.5;
            min-height: 100vh;
        }

        .container {
            max-width: 480px;
            margin: 0 auto;
            background-color: var(--color-bg-primary);
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .header {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            color: var(--color-text-white);
            padding: var(--spacing-6) var(--spacing-4);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-icon {
            width: var(--spacing-6);
            height: var(--spacing-6);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: var(--radius-base);
            transition: background-color var(--transition-base);
        }

        .header-icon:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .header-title {
            font-size: 1.125rem;
            font-weight: 500;
            flex: 1;
            text-align: center;
            margin-right: var(--spacing-6);
        }

        /* 主要内容区域 */
        .main-content {
            padding: var(--spacing-6) var(--spacing-4);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-6);
        }

        /* 卡片样式 */
        .card {
            background: var(--color-bg-white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }

        .card-body {
            padding: var(--spacing-6);
        }

        /* 我的资产卡片 */
        .assets-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-6);
        }

        .assets-icon {
            width: var(--spacing-8);
            height: var(--spacing-8);
            background-color: var(--color-primary);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--spacing-3);
        }

        .assets-title {
            font-size: 1.125rem;
            font-weight: 500;
            color: var(--color-text-primary);
        }

        .assets-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-8);
            margin-bottom: var(--spacing-6);
        }

        .asset-item {
            text-align: center;
        }

        .asset-label {
            color: var(--color-text-secondary);
            margin-bottom: var(--spacing-2);
            font-size: 0.875rem;
        }

        .asset-value {
            font-size: 1.5rem;
            font-weight: 300;
            color: var(--color-primary);
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-3) var(--spacing-4);
            border-radius: var(--radius-base);
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 1px solid transparent;
            min-height: 3.5rem;
        }

        .btn-primary {
            background-color: var(--color-primary);
            color: var(--color-text-white);
            border-color: var(--color-primary);
        }

        .btn-primary:hover {
            background-color: var(--color-primary-dark);
            border-color: var(--color-primary-dark);
        }

        .btn-outline {
            background-color: transparent;
            color: var(--color-text-tertiary);
            border-color: var(--color-border-normal);
        }

        .btn-outline:hover {
            background-color: var(--color-bg-primary);
            border-color: var(--color-border-dark);
            color: var(--color-text-primary);
        }

        .btn-ghost {
            background-color: transparent;
            color: var(--color-primary);
            border: none;
            padding: var(--spacing-2);
            min-height: var(--spacing-8);
            width: var(--spacing-8);
            border-radius: var(--radius-lg);
            transition: all var(--transition-base);
        }

        .btn-ghost:hover {
            background-color: var(--color-primary-alpha-10);
            color: var(--color-primary-dark);
        }

        .btn-ghost.copied {
            color: var(--color-success);
        }

        .btn-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-4);
        }

        /* 扫码充值区域 */
        .qr-section-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-6);
        }

        .qr-icon {
            color: var(--color-primary);
            margin-right: var(--spacing-3);
        }

        .qr-title {
            font-size: 1.125rem;
            font-weight: 500;
            color: var(--color-text-primary);
        }

        /* 支付方式 */
        .payment-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-4);
            margin-bottom: var(--spacing-6);
        }

        .payment-item {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-4);
            background-color: var(--color-bg-gray-50);
            border-radius: var(--radius-xl);
        }

        .payment-icon {
            width: var(--spacing-6);
            height: var(--spacing-6);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--spacing-3);
            font-size: 0.75rem;
            font-weight: bold;
            color: var(--color-text-white);
        }

        .payment-alipay {
            background-color: var(--color-info);
        }

        .payment-wechat {
            background-color: var(--color-success);
        }

        .payment-text {
            color: var(--color-text-tertiary);
        }

        /* 显示二维码按钮 */
        .qr-btn {
            width: 100%;
            margin-bottom: var(--spacing-6);
            font-size: 1.125rem;
        }

        /* ID区域 */
        .id-section {
            background-color: var(--color-bg-blue-50);
            border-radius: var(--radius-xl);
            padding: var(--spacing-4);
        }

        .id-content {
            display: flex;
            align-items: flex-start;
        }

        .id-info-icon {
            width: var(--spacing-6);
            height: var(--spacing-6);
            background-color: var(--color-info);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--spacing-3);
            margin-top: 0.125rem;
            flex-shrink: 0;
        }

        .id-text-content {
            flex: 1;
        }

        .id-instruction {
            color: var(--color-text-tertiary);
            margin-bottom: var(--spacing-2);
            font-size: 0.875rem;
        }

        .id-display {
            display: flex;
            align-items: center;
        }

        .id-label {
            color: var(--color-text-tertiary);
            margin-right: var(--spacing-2);
            font-size: 0.875rem;
        }

        .id-value {
            color: var(--color-primary);
            font-weight: 500;
            margin-right: var(--spacing-3);
        }

        /* 图标样式 */
        .icon {
            width: var(--icon-sm);
            height: var(--icon-sm);
        }

        .icon-lg {
            width: var(--icon-lg);
            height: var(--icon-lg);
        }

        .icon-white {
            color: var(--color-text-white);
        }

        .mr-2 {
            margin-right: var(--spacing-2);
        }

        /* Toast 提示样式 */
        .toast {
            position: fixed;
            bottom: var(--spacing-5);
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: var(--color-text-white);
            padding: var(--spacing-3) var(--spacing-5);
            border-radius: var(--radius-base);
            font-size: 0.875rem;
            z-index: 1000;
            opacity: 0;
            transition: opacity var(--transition-base);
        }

        .toast.show {
            opacity: 1;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="header-content">
                <div class="header-icon" onclick="history.back()">
                    <i data-lucide="arrow-left" class="icon-lg icon-white"></i>
                </div>
                <div class="header-title">充值与兑换</div>
                <div style="width: 24px;"></div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 我的资产卡片 -->
            <div class="card">
                <div class="card-body">
                    <div class="assets-header">
                        <div class="assets-icon">
                            <i data-lucide="wallet" class="icon icon-white"></i>
                        </div>
                        <h2 class="assets-title">我的资产</h2>
                    </div>

                    <div class="assets-grid">
                        <div class="asset-item">
                            <p class="asset-label">我的 RMB</p>
                            <p class="asset-value">¥0.00</p>
                        </div>
                        <div class="asset-item">
                            <p class="asset-label">我的妖晶</p>
                            <p class="asset-value">301,637</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="btn-grid">
                <button class="btn btn-outline" onclick="handleExchange()">
                    兑换
                </button>
                <button class="btn btn-primary" onclick="handleRecharge()">
                    充值
                </button>
            </div>

            <!-- 扫码充值区域 -->
            <div class="card">
                <div class="card-body">
                    <div class="qr-section-header">
                        <i data-lucide="qr-code" class="icon-lg qr-icon"></i>
                        <h3 class="qr-title">扫码充值</h3>
                    </div>

                    <!-- 支付方式 -->
                    <div class="payment-grid">
                        <div class="payment-item">
                            <div class="payment-icon payment-alipay">
                                <span>支</span>
                            </div>
                            <span class="payment-text">支付宝</span>
                        </div>
                        <div class="payment-item">
                            <div class="payment-icon payment-wechat">
                                <span>微</span>
                            </div>
                            <span class="payment-text">微信支付</span>
                        </div>
                    </div>

                    <!-- 显示二维码按钮 -->
                    <button class="btn btn-primary qr-btn" onclick="showQRCode()">
                        <i data-lucide="qr-code" class="icon mr-2"></i>
                        显示付款二维码
                    </button>

                    <!-- ID区域 -->
                    <div class="id-section">
                        <div class="id-content">
                            <div class="id-info-icon">
                                <i data-lucide="info" class="icon icon-white"></i>
                            </div>
                            <div class="id-text-content">
                                <p class="id-instruction">付款时请"添加备注"ID号</p>
                                <div class="id-display">
                                    <span class="id-label">你的ID号为：</span>
                                    <span class="id-value">11633</span>
                                    <button class="btn-ghost" id="copy-btn" onclick="copyID()">
                                        <i data-lucide="copy" class="icon" id="copy-icon"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // 复制ID功能
        function copyID() {
            const idText = "11633";
            const copyBtn = document.getElementById('copy-btn');
            const copyIcon = document.getElementById('copy-icon');
            
            // 复制到剪贴板
            navigator.clipboard.writeText(idText).then(function() {
                // 更改图标为对勾
                copyIcon.setAttribute('data-lucide', 'check');
                copyBtn.classList.add('copied');
                
                // 重新初始化图标
                lucide.createIcons();
                
                // 显示提示
                showToast('ID已复制到剪贴板');
                
                // 2秒后恢复原图标
                setTimeout(function() {
                    copyIcon.setAttribute('data-lucide', 'copy');
                    copyBtn.classList.remove('copied');
                    lucide.createIcons();
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败: ', err);
                showToast('复制失败，请手动复制');
            });
        }

        // 显示提示消息
        function showToast(message) {
            // 移除现有的toast
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }
            
            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = 'toast';
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            // 显示toast
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 200);
            }, 2000);
        }

        // 处理兑换按钮点击
        function handleExchange() {
            showToast('兑换功能开发中...');
        }

        // 处理充值按钮点击
        function handleRecharge() {
            showToast('充值功能开发中...');
        }

        // 显示二维码
        function showQRCode() {
            showToast('二维码功能开发中...');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有按钮添加点击效果
            const buttons = document.querySelectorAll('.btn, .header-icon');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // 添加点击动画效果
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            console.log('充值与兑换页面已加载完成');
        });
    </script>
</body>
</html>