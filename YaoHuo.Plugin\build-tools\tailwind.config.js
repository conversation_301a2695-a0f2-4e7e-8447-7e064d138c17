/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "../Template/**/*.{html,hbs,aspx,ascx,cshtml}", // 扫描 Template 目录下的模板文件
    "../**/*.{aspx,ascx,cshtml}", // 扫描项目根目录下的 ASP.NET 文件
    "../BBS/**/*.{aspx,ascx,cshtml}", // 扫描 BBS 目录
    "../Admin/**/*.{aspx,ascx,cshtml}", // 扫描 Admin 目录
    "../Pages/**/*.{aspx,ascx,cshtml}", // 扫描 Pages 目录
    "../WebSite/**/*.{aspx,ascx,cshtml}", // 扫描 WebSite 目录
    // 排除 node_modules, bin, obj 等目录
    "!../node_modules/**/*",
    "!../bin/**/*",
    "!../obj/**/*",
    "!../.git/**/*",
    "!../.vs/**/*"
  ],
  theme: {
    extend: {
      screens: {
        'xs-310': {'max': '310px'},
        'xs-350': {'max': '350px'},
        'xs-390': {'max': '390px'},
        'xs-480': {'max': '480px'},
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
      colors: {
        // ... 复制过来的颜色配置 ...
        'primary': '#58b4b0',
        'primary-dark': '#4a9c98',
        'primary-light': '#7cd0cb',
        'primary-alpha-10': 'rgba(88, 180, 176, 0.1)',
        'primary-alpha-30': 'rgba(88, 180, 176, 0.3)',
        'primary-alpha-05': 'rgba(88, 180, 176, 0.05)',
        'primary-alpha-20': 'rgba(88, 180, 176, 0.2)',

        // 文本颜色
        'text-primary': '#1f2937',
        'text-secondary': '#6b7280',
        'text-tertiary': '#4b5563',
        'text-light': '#9ca3af',

        // 状态颜色
        'success': '#10b981',
        'danger': '#dc2626',
        'danger-dark': '#b91c1c',
        'warning': '#d97706',
        'warning-light': '#eab308',
        'warning-alpha-20': 'rgba(217, 119, 6, 0.2)',
        'error': '#ef4444',
        'info': '#3b82f6',

        // 绿色系列
        'green-50': '#f0fdf4',
        'green-500': '#22c55e',
        'green-600': '#16a34a',
        'green-700': '#15803d',
        'green-800': '#166534',

        // 蓝色系列
        'blue-50': '#eff6ff',
        'blue-500': '#3b82f6',
        'blue-600': '#2563eb',
        'blue-700': '#1d4ed8',
        'blue-800': '#1e40af',

        // 灰色系列
        'gray-50': '#f9fafb',
        'gray-100': '#f3f4f6',
        'gray-400': '#9ca3af',
        'gray-500': '#6b7280',
        'gray-600': '#4b5563',
        'gray-700': '#374151',
        'gray-800': '#1f2937',

        // 背景颜色
        'bg-primary': '#f9fafb',
        'bg-gray-50': '#f9fafb',
        'bg-gray-100': '#f3f4f6',

        // 边框颜色
        'border-light': '#f3f4f6',
        'border-normal': '#e5e7eb',
        'border-dark': '#d1d5db',

        // 特殊背景色
        'bg-vip': '#fef2f2',
        'bg-admin': '#fef3c7',
        'bg-medal': '#fef3c7',
        'bg-error': '#fee2e2',
        'bg-info': '#e0f2fe',
      },
      spacing: {
        // ... 复制过来的间距配置 ...
        '0': '0',
        '1': '0.25rem', // 4px
        '2': '0.5rem',  // 8px
        '3': '0.75rem', // 12px
        '4': '1rem',    // 16px
        '5': '1.25rem', // 20px
        '6': '1.5rem',  // 24px
        '8': '2rem',    // 32px
        '10': '2.5rem', // 40px
        '12': '3rem',   // 48px
        '16': '4rem',   // 64px
        '20': '5rem',   // 80px
      },
      fontSize: {
        // ... 复制过来的字体大小配置 ...
        'xs': '0.75rem',   // 12px
        'sm': '0.875rem',  // 14px
        'base': '1rem',    // 16px
        'lg': '1.125rem',  // 18px
        'xl': '1.25rem',   // 20px
        '2xl': '1.5rem',   // 24px
        '3xl': '1.875rem', // 30px
      },
      fontWeight: {
        // ... 复制过来的字体粗细配置 ...
        'normal': '400',
        'medium': '500',
        'semibold': '600',
        'bold': '700',
      },
      borderRadius: {
        // ... 复制过来的圆角配置 ...
        'none': '0',
        'sm': '0.25rem',      // 4px
        'DEFAULT': '0.375rem', // 6px
        'md': '0.5rem',       // 8px
        'lg': '0.75rem',      // 12px
        'xl': '1rem',         // 16px
        'full': '9999px',     // 胶囊形
        'circle': '50%',      // 圆形
      },
      boxShadow: {
        // ... 复制过来的阴影配置 ...
        'sm': '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        'DEFAULT': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'lg': '0 4px 6px rgba(0, 0, 0, 0.1)',
        'dialog': '0 10px 25px rgba(0,0,0,0.15)', // 自定义对话框阴影
      },
      zIndex: {
        // ... 复制过来的 zIndex 配置 ...
        'dropdown': '10',
        'modal': '1000',
        'toast': '1001',
      },
      transitionProperty: {
        // ... 复制过来的过渡属性配置 ...
        'DEFAULT': 'all',
      },
      transitionDuration: {
        // ... 复制过来的过渡时间配置 ...
        'fast': '0.15s',
        'DEFAULT': '0.2s',
        'slow': '0.3s',
        'extra-slow': '1.2s',
      },
      transitionTimingFunction: {
        // ... 复制过来的过渡 timing function 配置 ...
        'ease-out': 'cubic-bezier(0, 0, 0.2, 1)',
        'ease-in': 'cubic-bezier(0.4, 0, 1, 1)',
        'ease-in-out': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      keyframes: {
        // ... 复制过来的 keyframes 配置 ...
        expandFields: {
          'from': { opacity: '0', transform: 'translateY(-10px)' },
          'to': { opacity: '1', transform: 'translateY(0)' }
        },
        collapseFields: {
          'from': { opacity: '1', transform: 'translateY(0)' },
          'to': { opacity: '0', transform: 'translateY(-10px)' }
        }
      },
      animation: {
        // ... 复制过来的 animation 配置 ...
        expandFields: 'expandFields 0.2s ease-in-out',
        collapseFields: 'collapseFields 0.2s ease-in-out'
      },
      width: {
        // ... 复制过来的 width 配置 ...
        'icon-xs': '0.875rem', // 14px
        'icon-sm': '1rem', // 16px
        'icon-base': '1.25rem', // 20px
        'icon-lg': '1.5rem', // 24px
        'icon-xl': '2rem', // 32px
        'icon-2xl': '2.5rem', // 40px
      },
      height: {
        // ... 复制过来的 height 配置 ...
        'icon-xs': '0.875rem', // 14px
        'icon-sm': '1rem', // 16px
        'icon-base': '1.25rem', // 20px
        'icon-lg': '1.5rem', // 24px
        'icon-xl': '2rem', // 32px
        'icon-2xl': '2.5rem', // 40px
      },
    },
  },
  plugins: [
    function({ addBase }) {
      addBase({
        // 修复UBB标签中图片显示问题，使其与文本保持在同一行
        '.text-gray-900 img': {
          'display': 'inline',
          'vertical-align': 'middle'
        }
      });
    }
  ],
}

