<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{48C5B458-B7AE-4666-AB6B-F4F8FE35DB24}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>YaoHuo.Plugin</RootNamespace>
    <AssemblyName>YaoHuo.Plugin</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AspNetPager">
      <HintPath>Lib\AspNetPager.dll</HintPath>
    </Reference>
    <Reference Include="GamesClassManager, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\GamesClassManager.dll</HintPath>
    </Reference>
    <Reference Include="KeLink.Com, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\KeLink.Com.dll</HintPath>
    </Reference>
    <Reference Include="KeLin_WebSite, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\KeLin_WebSite.dll</HintPath>
    </Reference>
    <Reference Include="KenLin_ClassManager, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\KenLin_ClassManager.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="UBB_Expand, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\UBB_Expand.dll</HintPath>
    </Reference>
    <Reference Include="WeiXinClassManager">
      <HintPath>Lib\WeiXinClassManager.dll</HintPath>
    </Reference>
    <Reference Include="WwwClassManager, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\WwwClassManager.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Admin\AddDownWAPALL.aspx" />
    <Content Include="Admin\AddRowInfoWAP.aspx" />
    <Content Include="Admin\AddTopWAP.aspx" />
    <Content Include="Admin\Admin_WapClasslist.aspx" />
    <Content Include="Admin\BaseSiteModifyWML.aspx" />
    <Content Include="Admin\BaseSiteModifyWML00.aspx" />
    <Content Include="Album\Admin_WAPadd.aspx" />
    <Content Include="Album\AlbumList.aspx" />
    <Content Include="Album\AlbumList_del.aspx" />
    <Content Include="Album\Book_View.aspx" />
    <Content Include="BBS\Admin_guestlistWAP.aspx" />
    <Content Include="BBS\Admin_guestlistWAP00.aspx" />
    <Content Include="BBS\Admin_guestlistWAPdel00.aspx" />
    <Content Include="BBS\Admin_userlistWAP.aspx" />
    <Content Include="BBS\Admin_userlistWAP00.aspx" />
    <Content Include="BBS\Banklist.aspx" />
    <Content Include="BBS\Book_List_delmy.aspx" />
    <Content Include="BBS\Book_List_hot.aspx" />
    <Content Include="BBS\Book_List_rank.aspx" />
    <Content Include="BBS\Book_Re_addfile.aspx" />
    <Content Include="BBS\Book_Re_addfileshow.aspx" />
    <Content Include="BBS\Book_Re_del.aspx" />
    <Content Include="BBS\Book_Re_delmy.aspx" />
    <Content Include="BBS\Book_Re_mod.aspx" />
    <Content Include="BBS\Book_Re_my.aspx" />
    <Content Include="BBS\Book_Re_top.aspx" />
    <Content Include="BBS\Book_List_Search.aspx" />
    <Content Include="BBS\Book_Search_New.aspx" />
    <Content Include="BBS\Book_View_add.aspx" />
    <Content Include="BBS\Book_View_addfile.aspx" />
    <Content Include="BBS\Book_View_addfileaddurl.aspx" />
    <Content Include="BBS\Book_View_addurl.aspx" />
    <Content Include="BBS\Book_View_addvote.aspx" />
    <Content Include="BBS\Book_View_admin.aspx" />
    <Content Include="BBS\Book_View_change.aspx" />
    <Content Include="BBS\Book_View_del.aspx" />
    <Content Include="BBS\Book_View_down.aspx" />
    <Content Include="BBS\Book_View_end.aspx" />
    <Content Include="BBS\Book_View_good.aspx" />
    <Content Include="BBS\Book_View_lock.aspx" />
    <Content Include="BBS\Book_View_mod.aspx" />
    <Content Include="BBS\Book_View_modfile_del.aspx" />
    <Content Include="BBS\Book_View_sendmoney.aspx" />
    <Content Include="BBS\Book_View_top.aspx" />
    <Content Include="BBS\Book_View_tovote.aspx" />
    <Content Include="BBS\Book_View_ubb.aspx" />
    <Content Include="BBS\Control\FaceAndReply.ascx" />
    <Content Include="BBS\Control\GuessingModule.ascx" />
    <Content Include="BBS\Control\LikeModule.ascx" />
    <Content Include="BBS\CreateUser.aspx" />
    <Content Include="BBS\Default.aspx" />
    <Content Include="BBS\Download.aspx" />
    <Content Include="BBS\EditProfile.aspx" />
    <Content Include="BBS\Favlist.aspx" />
    <Content Include="BBS\Favlist_del.aspx" />
    <Content Include="BBS\Friendlist_del.aspx" />
    <Content Include="BBS\Friendlist_mod.aspx" />
    <Content Include="BBS\GuessAdd.aspx" />
    <Content Include="BBS\GuessMod.aspx" />
    <Content Include="BBS\GuessVote.aspx" />
    <Content Include="BBS\LockUser_List_add.aspx" />
    <Content Include="BBS\LockUser_List_del.aspx" />
    <Content Include="BBS\MessageList_add.aspx" />
    <Content Include="BBS\MessageList_View.aspx" />
    <Content Include="BBS\ModifyHead.aspx" />
    <Content Include="BBS\ModifyInfo.aspx" />
    <Content Include="BBS\ModifyNick.aspx" />
    <Content Include="BBS\ModifyPW.aspx" />
    <Content Include="BBS\ModifyRemark.aspx" />
    <Content Include="BBS\ModifyUserName.aspx" />
    <Content Include="BBS\Report_Add.aspx" />
    <Content Include="BBS\Report_List.aspx" />
    <Content Include="BBS\Report_List_del.aspx" />
    <Content Include="BBS\ResetVIP.aspx" />
    <Content Include="BBS\RMBtoMoney.aspx" />
    <Content Include="BBS\Settings.aspx" />
    <Content Include="BBS\Share.aspx" />
    <Content Include="BBS\ToBBSType.aspx" />
    <Content Include="BBS\ToGroupBuy.aspx" />
    <Content Include="BBS\ToGroupCoinBuy.aspx" />
    <Content Include="BBS\ToMedal.aspx" />
    <Content Include="BBS\UserGuessBook.aspx" />
    <Content Include="Games\Chat\Admin_userlistWAP.aspx" />
    <Content Include="Games\Chat\Admin_WAPdel.aspx" />
    <Content Include="Games\Chat\Book_Re.aspx" />
    <Content Include="Games\ChuiNiu\Add.aspx" />
    <Content Include="Games\ChuiNiu\Book_List.aspx" />
    <Content Include="Games\ChuiNiu\Book_View.aspx" />
    <Content Include="Games\ChuiNiu\ClassConfigAll.aspx" />
    <Content Include="Games\ChuiNiu\Doit.aspx" />
    <Content Include="Games\ChuiNiu\Index.aspx" />
    <Content Include="Games\GamesIndex.aspx" />
    <Content Include="Games\Index.aspx" />
    <Content Include="Games\Rank\Book_List.aspx" />
    <Content Include="Lib\AspNetPager.dll" />
    <Content Include="Lib\GamesClassManager.dll" />
    <Content Include="Lib\KeLink.Com.dll" />
    <Content Include="Lib\KeLin_WebSite.dll" />
    <Content Include="Lib\KenLin_ClassManager.dll" />
    <Content Include="Lib\UBB_Expand.dll" />
    <Content Include="Lib\WeiXinClassManager.dll" />
    <Content Include="Lib\WwwClassManager.dll" />
    <Content Include="MyFile.aspx" />
    <Content Include="NetCSS\BookView\NewReply.css" />
    <Content Include="NetCSS\BookView\SimpleSearch.css" />
    <Content Include="NetCSS\CSS\BBS\Betting.css" />
    <Content Include="NetCSS\CSS\BBS\BookRe-emoji.css" />
    <Content Include="NetCSS\CSS\BBS\Fonts\DefaultPage.css" />
    <Content Include="NetCSS\CSS\BBS\GroupBuy.css" />
    <Content Include="NetCSS\CSS\BBS\Link-dark.css" />
    <Content Include="NetCSS\CSS\BBS\Link-light.css" />
    <Content Include="NetCSS\CSS\BBS\Popup-Base.css" />
    <Content Include="NetCSS\CSS\BBS\SendMoney_freeMain.css" />
    <Content Include="NetCSS\CSS\ChatMessage.css" />
    <Content Include="NetCSS\CSS\Index\404-Dark.css" />
    <Content Include="NetCSS\CSS\Index\404-Light.css" />
    <Content Include="NetCSS\CSS\Index\Default\default.css" />
    <Content Include="NetCSS\CSS\Index\Default\xqx.css" />
    <Content Include="NetCSS\CSS\Index\WapStyle-Dark.css" />
    <Content Include="NetCSS\CSS\Index\WapStyle-Light.css" />
    <Content Include="NetCSS\CSS\Login\Fonts\fontawesome-webfont.svg" />
    <Content Include="NetCSS\CSS\Login\Gocaptcha\gocaptcha-init.js" />
    <Content Include="NetCSS\CSS\Login\Gocaptcha\gocaptcha-modal.css" />
    <Content Include="NetCSS\CSS\Login\Gocaptcha\gocaptcha.global.css" />
    <Content Include="NetCSS\CSS\Login\Gocaptcha\gocaptcha.global.js" />
    <Content Include="NetCSS\CSS\Login\Home.js" />
    <Content Include="NetCSS\CSS\Login\IMG\Arm-Down-Left%402x.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Arm-Down-Left.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Arm-Down-Right%402x.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Arm-Down-Right.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Arm-Up-Left%402x.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Arm-Up-Left.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Arm-Up-Right%402x.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Arm-Up-Right.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Eyes%402x.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Eyes.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Face%402x.png" />
    <Content Include="NetCSS\CSS\Login\IMG\Face.png" />
    <Content Include="NetCSS\CSS\Login\Style.css" />
    <Content Include="NetCSS\CSS\Login\Vue.Min.2.6.10.js" />
    <Content Include="NetCSS\CSS\Login\Zero.js" />
    <Content Include="NetCSS\CSS\NeedPW.css" />
    <Content Include="NetCSS\CSS\PawClap-Style.css" />
    <Content Include="NetCSS\CSS\Save-Notification.css" />
    <Content Include="NetCSS\CSS\Setting.css" />
    <Content Include="NetCSS\CSS\Upload-Resource.css" />
    <Content Include="NetCSS\JS\BBSetting.js" />
    <Content Include="NetCSS\JS\BBS\IdentityPurchase.js" />
    <Content Include="NetCSS\JS\BookRe\BookReScript.js" />
    <Content Include="NetCSS\JS\BookView\Attach.js" />
    <Content Include="NetCSS\JS\BookView\AtUserID.js" />
    <Content Include="NetCSS\JS\BookView\BookViewScript.js" />
    <Content Include="NetCSS\JS\BookView\Emoji.js" />
    <Content Include="NetCSS\JS\BookView\Guess\Betting.js" />
    <Content Include="NetCSS\JS\BookView\Guess\Celebration.js" />
    <Content Include="NetCSS\JS\BookView\Guess\Confetti.Browser.min.js" />
    <Content Include="NetCSS\JS\BookView\IframePopupManager.js" />
    <Content Include="NetCSS\JS\BookView\ImageStyle.js" />
    <Content Include="NetCSS\JS\BookView\ItemDisplay.js" />
    <Content Include="NetCSS\JS\BookView\Reward.js" />
    <Content Include="NetCSS\JS\BookView\VoteOptimize.js" />
    <Content Include="NetCSS\JS\Message.js" />
    <Content Include="NetCSS\JS\Shared\DomHelpers.js" />
    <Content Include="NetCSS\JS\Shared\NewReplyUI.js" />
    <Content Include="NetCSS\JS\Shared\QuickReplyAjax.js" />
    <Content Include="NetCSS\JS\Shared\ReplyForm.js" />
    <Content Include="NetCSS\KL_Common.js" />
    <Content Include="Pages\Search\ToManager.aspx" />
    <Content Include="Pages\Search\Search.aspx" />
    <Content Include="Template\CSS\output.css" />
    <Content Include="WapIndex.aspx" />
    <Content Include="WapLogin.aspx" />
    <Content Include="WapLogout.aspx" />
    <Content Include="Web.config" />
    <Content Include="BBS\Book_List.aspx" />
    <Content Include="BBS\Book_Re.aspx" />
    <Content Include="BBS\Book_View.aspx" />
    <Content Include="BBS\Book_View_addfileAdd.aspx" />
    <Content Include="BBS\Book_View_modadd.aspx" />
    <Content Include="BBS\Book_View_modfile.aspx" />
    <Content Include="BBS\Index.aspx" />
    <Content Include="BBS\MessageList.aspx" />
    <Content Include="BBS\MessageList_Clear.aspx" />
    <Content Include="BBS\MessageList_Del.aspx" />
    <Content Include="BBS\SendMoney.aspx" />
    <Content Include="BBS\SendMoney_Free.aspx" />
    <Content Include="BBS\SendMoney_FreeMain.aspx" />
    <Content Include="BBS\ToMoney.aspx" />
    <Content Include="BBS\ToMyBankMoney.aspx" />
    <Content Include="BBS\UserInfo.aspx" />
    <Content Include="BBS\View.aspx" />
    <Content Include="BBS\ViewUser.aspx" />
    <Content Include="BBS\FriendList.aspx" />
    <Content Include="BBS\List.aspx" />
    <Content Include="WML\Admin_userlistWAP.aspx" />
    <Content Include="WML\Admin_WAPmodify.aspx" />
    <Content Include="WML\Index.aspx" />
    <Content Include="XinZhang\Book_View_Buy.aspx" />
    <Content Include="XinZhang\Book_View_My.aspx" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Admin\AddDownWAPALL.aspx.cs">
      <DependentUpon>AddDownWAPALL.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\AddRowInfoWAP.aspx.cs">
      <DependentUpon>AddRowInfoWAP.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\AddTopWAP.aspx.cs">
      <DependentUpon>AddTopWAP.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\Admin_WapClasslist.aspx.cs">
      <DependentUpon>Admin_WapClasslist.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\BaseSiteModifyWML.aspx.cs">
      <DependentUpon>BaseSiteModifyWML.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\BaseSiteModifyWML00.aspx.cs">
      <DependentUpon>BaseSiteModifyWML00.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Album\Admin_WAPadd.aspx.cs">
      <DependentUpon>Admin_WAPadd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Album\AlbumList.aspx.cs">
      <DependentUpon>AlbumList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Album\AlbumList_del.aspx.cs">
      <DependentUpon>AlbumList_del.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Album\Book_View.aspx.cs">
      <DependentUpon>Book_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Admin_guestlistWAP.aspx.cs">
      <DependentUpon>Admin_guestlistWAP.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Admin_guestlistWAP00.aspx.cs">
      <DependentUpon>Admin_guestlistWAP00.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Admin_guestlistWAPdel00.aspx.cs">
      <DependentUpon>Admin_guestlistWAPdel00.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Admin_userlistWAP.aspx.cs">
      <DependentUpon>Admin_userlistWAP.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Admin_userlistWAP00.aspx.cs">
      <DependentUpon>Admin_userlistWAP00.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\API\GetReplyInfo.ashx.cs">
      <DependentUpon>GetReplyInfo.ashx</DependentUpon>
    </Compile>
    <Compile Include="BBS\Banklist.aspx.cs">
      <DependentUpon>Banklist.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_List.aspx.cs">
      <DependentUpon>Book_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_List_delmy.aspx.cs">
      <DependentUpon>Book_List_delmy.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_List_hot.aspx.cs">
      <DependentUpon>Book_List_hot.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_List_rank.aspx.cs">
      <DependentUpon>Book_List_rank.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_Re.aspx.cs">
      <DependentUpon>Book_Re.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_Re_addfile.aspx.cs">
      <DependentUpon>Book_Re_addfile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_Re_addfileshow.aspx.cs">
      <DependentUpon>Book_Re_addfileshow.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_Re_del.aspx.cs">
      <DependentUpon>Book_Re_del.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_Re_delmy.aspx.cs">
      <DependentUpon>Book_Re_delmy.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_Re_mod.aspx.cs">
      <DependentUpon>Book_Re_mod.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_Re_my.aspx.cs">
      <DependentUpon>Book_Re_my.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_Re_top.aspx.cs">
      <DependentUpon>Book_Re_top.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_List_Search.aspx.cs">
      <DependentUpon>Book_List_Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_Search_New.aspx.cs">
      <DependentUpon>Book_Search_New.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View.aspx.cs">
      <DependentUpon>Book_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_add.aspx.cs">
      <DependentUpon>Book_View_add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_addfile.aspx.cs">
      <DependentUpon>Book_View_addfile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_addfileAdd.aspx.cs">
      <DependentUpon>Book_View_addfileAdd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_addfileaddurl.aspx.cs">
      <DependentUpon>Book_View_addfileaddurl.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_addurl.aspx.cs">
      <DependentUpon>Book_View_addurl.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_addvote.aspx.cs">
      <DependentUpon>Book_View_addvote.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_admin.aspx.cs">
      <DependentUpon>Book_View_admin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_change.aspx.cs">
      <DependentUpon>Book_View_change.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_del.aspx.cs">
      <DependentUpon>Book_View_del.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_down.aspx.cs">
      <DependentUpon>Book_View_down.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_end.aspx.cs">
      <DependentUpon>Book_View_end.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_good.aspx.cs">
      <DependentUpon>Book_View_good.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_lock.aspx.cs">
      <DependentUpon>Book_View_lock.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_mod.aspx.cs">
      <DependentUpon>Book_View_mod.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_modadd.aspx.cs">
      <DependentUpon>Book_View_modadd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_modfile.aspx.cs">
      <DependentUpon>Book_View_modfile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_modfile_del.aspx.cs">
      <DependentUpon>Book_View_modfile_del.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_sendmoney.aspx.cs">
      <DependentUpon>Book_View_sendmoney.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_top.aspx.cs">
      <DependentUpon>Book_View_top.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_tovote.aspx.cs">
      <DependentUpon>Book_View_tovote.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Book_View_ubb.aspx.cs">
      <DependentUpon>Book_View_ubb.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Control\FaceAndReply.ascx.cs">
      <DependentUpon>FaceAndReply.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Control\FaceAndReply.ascx.designer.cs">
      <DependentUpon>FaceAndReply.ascx</DependentUpon>
    </Compile>
    <Compile Include="BBS\Control\GuessingModule.ascx.cs">
      <DependentUpon>GuessingModule.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Control\GuessingModule.ascx.designer.cs">
      <DependentUpon>GuessingModule.ascx</DependentUpon>
    </Compile>
    <Compile Include="BBS\Control\LikeModule.ascx.cs">
      <DependentUpon>LikeModule.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Control\LikeModule.ascx.designer.cs">
      <DependentUpon>LikeModule.ascx</DependentUpon>
    </Compile>
    <Compile Include="BBS\CreateUser.aspx.cs">
      <DependentUpon>CreateUser.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Download.aspx.cs">
      <DependentUpon>Download.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\EditProfile.aspx.cs">
      <DependentUpon>EditProfile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Favlist.aspx.cs">
      <DependentUpon>Favlist.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Favlist_del.aspx.cs">
      <DependentUpon>Favlist_del.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Friendlist_del.aspx.cs">
      <DependentUpon>Friendlist_del.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Friendlist_mod.aspx.cs">
      <DependentUpon>Friendlist_mod.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\GuessAdd.aspx.cs">
      <DependentUpon>GuessAdd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\GuessMod.aspx.cs">
      <DependentUpon>GuessMod.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\GuessVote.aspx.cs">
      <DependentUpon>GuessVote.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Index.aspx.cs">
      <DependentUpon>Index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\LockUser_List_add.aspx.cs">
      <DependentUpon>LockUser_List_add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\LockUser_List_del.aspx.cs">
      <DependentUpon>LockUser_List_del.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\MessageList.aspx.cs">
      <DependentUpon>MessageList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\MessageList_add.aspx.cs">
      <DependentUpon>MessageList_add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\MessageList_Clear.aspx.cs">
      <DependentUpon>MessageList_Clear.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\MessageList_Del.aspx.cs">
      <DependentUpon>MessageList_Del.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\MessageList_View.aspx.cs">
      <DependentUpon>MessageList_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ModifyHead.aspx.cs">
      <DependentUpon>ModifyHead.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ModifyPW.aspx.cs">
      <DependentUpon>ModifyPW.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\RMBtoMoney.aspx.cs">
      <DependentUpon>RMBtoMoney.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="GoCaptchaProxy.ashx.cs">
      <DependentUpon>GoCaptchaProxy.ashx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Search\Search.aspx.cs">
      <DependentUpon>Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Template\Models\BankListPageModel.cs" />
    <Compile Include="Template\Models\BookReMyPageModel.cs" />
    <Compile Include="Template\Models\CommonModels.cs" />
    <Compile Include="Template\Models\EditProfilePageModel.cs" />
    <Compile Include="Template\Models\FavListPageModel.cs" />
    <Compile Include="Template\Models\FriendListPageModel.cs" />
    <Compile Include="Template\Models\HeaderOptionsModel.cs" />
    <Compile Include="Template\Models\ModifyHeadPageModel.cs" />
    <Compile Include="Template\Models\ModifyPasswordPageModel.cs" />
    <Compile Include="BBS\Models\RMBtoMoneyPageModel.cs" />
    <Compile Include="BBS\ModifyInfo.aspx.cs">
      <DependentUpon>ModifyInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ModifyNick.aspx.cs">
      <DependentUpon>ModifyNick.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ModifyRemark.aspx.cs">
      <DependentUpon>ModifyRemark.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ModifyUserName.aspx.cs">
      <DependentUpon>ModifyUserName.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Report_Add.aspx.cs">
      <DependentUpon>Report_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Report_List.aspx.cs">
      <DependentUpon>Report_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Report_List_del.aspx.cs">
      <DependentUpon>Report_List_del.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ResetVIP.aspx.cs">
      <DependentUpon>ResetVIP.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\SendMoney.aspx.cs">
      <DependentUpon>SendMoney.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\SendMoney_Free.aspx.cs">
      <DependentUpon>SendMoney_Free.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\SendMoney_FreeMain.aspx.cs">
      <DependentUpon>SendMoney_FreeMain.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Settings.aspx.cs">
      <DependentUpon>Settings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\Share.aspx.cs">
      <DependentUpon>Share.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ToBBSType.aspx.cs">
      <DependentUpon>ToBBSType.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ToGroupBuy.aspx.cs">
      <DependentUpon>ToGroupBuy.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ToGroupCoinBuy.aspx.cs">
      <DependentUpon>ToGroupCoinBuy.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ToMedal.aspx.cs">
      <DependentUpon>ToMedal.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ToMoney.aspx.cs">
      <DependentUpon>ToMoney.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ToMyBankMoney.aspx.cs">
      <DependentUpon>ToMyBankMoney.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\UserGuessBook.aspx.cs">
      <DependentUpon>UserGuessBook.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\UserInfo.aspx.cs">
      <DependentUpon>UserInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\View.aspx.cs">
      <DependentUpon>View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\ViewUser.aspx.cs">
      <DependentUpon>ViewUser.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\FriendList.aspx.cs">
      <DependentUpon>FriendList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BBS\List.aspx.cs">
      <DependentUpon>List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WapLogout.aspx.cs">
      <DependentUpon>WapLogout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebSite\Tool\Conversion.cs" />
    <Compile Include="Games\Chat\Admin_userlistWAP.aspx.cs">
      <DependentUpon>Admin_userlistWAP.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\Chat\Admin_WAPdel.aspx.cs">
      <DependentUpon>Admin_WAPdel.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\Chat\Book_Re.aspx.cs">
      <DependentUpon>Book_Re.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\ChuiNiu\Add.aspx.cs">
      <DependentUpon>Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\ChuiNiu\Book_List.aspx.cs">
      <DependentUpon>Book_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\ChuiNiu\Book_View.aspx.cs">
      <DependentUpon>Book_View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\ChuiNiu\ClassConfigAll.aspx.cs">
      <DependentUpon>ClassConfigAll.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\ChuiNiu\Doit.aspx.cs">
      <DependentUpon>Doit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\ChuiNiu\Index.aspx.cs">
      <DependentUpon>Index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\GamesIndex.aspx.cs">
      <DependentUpon>GamesIndex.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\Index.aspx.cs">
      <DependentUpon>Index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Games\Rank\Book_List.aspx.cs">
      <DependentUpon>Book_List.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Global.asax.cs" />
    <Compile Include="MyFile.aspx.cs">
      <DependentUpon>MyFile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Pages\Search\ToManager.aspx.cs">
      <DependentUpon>ToManager.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebSite\BBS\Helper\BBSHelper.cs" />
    <Compile Include="WebSite\BBS\Service\UserBlockingService.cs" />
    <Compile Include="WebSite\BBS\Helper\ReplyHelper.cs" />
    <Compile Include="WebSite\BBS\Repository\UserPreferencesRepository.cs" />
    <Compile Include="WebSite\BBS\Base\BaseBBSListPage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebSite\BBS\Helper\FileUploadHelper.cs" />
    <Compile Include="WebSite\Services\FavQueryService.cs" />
    <Compile Include="WebSite\Services\FriendQueryService.cs" />
    <Compile Include="WebSite\Tool\HttpTool.cs" />
    <Compile Include="WebSite\Tool\MessageTool.cs" />
    <Compile Include="WebSite\BBS\Repository\ReplyRepository.cs" />
    <Compile Include="WebSite\BBS\Helper\TinyPngHelper.cs" />
    <Compile Include="WebSite\BBS\Service\AttachmentService.cs" />
    <Compile Include="WebSite\BBS\Service\BBSGuessService.cs" />
    <Compile Include="WebSite\BBS\Service\BBSLikeService.cs" />
    <Compile Include="WebSite\BLL\WapVCountEveryDateBLL.cs" />
    <Compile Include="WebSite\BBS\Helper\BookViewHelper.cs" />
    <Compile Include="WebSite\DAL\WapVCountEveryDateDAL.cs" />
    <Compile Include="WebSite\BBS\Model\GuessData.cs" />
    <Compile Include="WebSite\Tool\MyCount.cs" />
    <Compile Include="WebSite\Tool\MyPageWap.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebSite\BBS\Service\SendMoneyService.cs" />
    <Compile Include="WebSite\Services\CaptchaService.cs" />
    <Compile Include="WebSite\Services\TemplateService.cs" />
    <Compile Include="WebSite\Tool\UserPageModel.cs" />
    <Compile Include="WebSite\Tool\WebTool.cs" />
    <Compile Include="WapIndex.aspx.cs">
      <DependentUpon>WapIndex.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WapLogin.aspx.cs">
      <DependentUpon>WapLogin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WML\Admin_userlistWAP.aspx.cs">
      <DependentUpon>Admin_userlistWAP.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WML\Admin_WAPmodify.aspx.cs">
      <DependentUpon>Admin_WAPmodify.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WML\Index.aspx.cs">
      <DependentUpon>Index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="XinZhang\Book_View_Buy.aspx.cs">
      <DependentUpon>Book_View_Buy.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="XinZhang\Book_View_My.aspx.cs">
      <DependentUpon>Book_View_My.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>58946</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:58946/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <ItemGroup>
    <Reference Include="System.Runtime.Caching">
      <HintPath>C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.0\System.Runtime.Caching.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="NetCSS\CSS\Login\Fonts\fontawesome-webfont.eot" />
    <Content Include="NetCSS\CSS\Login\Fonts\fontawesome-webfont.ttf" />
    <Content Include="NetCSS\CSS\Login\Fonts\fontawesome-webfont.woff" />
    <Content Include="NetCSS\CSS\Login\Fonts\fontawesome-webfont.woff2" />
    <Content Include="NetCSS\CSS\Login\Fonts\ReadMe-Icons.eot" />
    <Content Include="NetCSS\CSS\Login\Fonts\ReadMe-Icons.ttf" />
    <Content Include="NetCSS\CSS\Login\Fonts\ReadMe-Icons.woff" />
    <Content Include="NetCSS\CSS\Login\Fonts\ReadMe-Icons.woff2" />
    <Content Include="BBS\API\GetReplyInfo.ashx" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Handlebars.Net">
      <Version>2.1.6</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" Version="4.1.0" />
    <PackageReference Include="Microsoft.Net.Http" Version="2.2.29" />
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="TencentCloudSDK.Captcha">
      <Version>3.0.1249</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="NetCSS\JS\BookView\Emoji.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Partials\Header.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Pages\MyFile.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Layouts\MainLayout.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Pages\BankList.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Pages\EditProfile.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Pages\ModifyPassword.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Pages\ModifyHead.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Pages\FriendList.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Pages\BookReMy.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Pages\FavList.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Pages\RMBtoMoney.hbs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="GoCaptchaProxy.ashx" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="rewriteRules.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="build-tools\go-captcha\config.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="build-tools\go-captcha\gocaptcha.json" />
  </ItemGroup>
</Project>