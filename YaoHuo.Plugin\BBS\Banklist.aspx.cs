using System;
using System.Collections.Generic;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.Template.Models;
using YaoHuo.Plugin.BBS.Models;

namespace YaoHuo.Plugin.BBS
{
    public class BankList : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string friendtype = "";

        public string backurl = "";

        public string linkTOP = "";

        public string toyear = DateTime.Now.Year.ToString();

        public string tomonth = DateTime.Now.Month.ToString();

        public bool isadmin = false;

        public string typeid = "";

        public string typekey = "";

        public List<wap_bankLog_Model> listVo = null;

        public long long_0 = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            isadmin = IsUserManager(userid, userVo.managerlvl, "");
            IsLogin(userid, backurl);

            // 检查UI偏好并尝试渲染新版UI
            if (CheckAndHandleUIPreference())
            {
                return; // 如果成功渲染新版UI，则直接返回
            }

            // 继续执行原有逻辑
            switch (action)
            {
                case "class":
                    showclass();
                    break;
                default:
                    showclass();
                    break;
                case "godel":
                    break;
            }
        }

        /// <summary>
        /// 检查UI偏好并处理新版UI渲染
        /// </summary>
        /// <returns>如果成功渲染新版UI则返回true，否则返回false</returns>
        private bool CheckAndHandleUIPreference()
        {
            try
            {
                string viewMode = TemplateService.GetViewMode();
                if (viewMode == "new")
                {
                    return TryRenderWithHandlebars();
                }
            }
            catch (Exception ex)
            {
                // 如果新版渲染失败，记录错误但继续使用旧版
                System.Diagnostics.Debug.WriteLine($"BankList新版UI渲染检查失败: {ex.Message}");
            }
            return false;
        }

        /// <summary>
        /// 尝试使用Handlebars渲染新版UI
        /// </summary>
        /// <returns>渲染是否成功</returns>
        private bool TryRenderWithHandlebars()
        {
            try
            {
                // 先执行数据获取逻辑
                PrepareDataForHandlebars();

                // 构建页面数据模型
                var pageModel = BuildBankListPageModel();

                // 渲染页面
                RenderWithHandlebars(pageModel);

                return true;
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException是Response.End()的正常行为
                return true;
            }
            catch (Exception ex)
            {
                // 记录错误，回退到旧版UI
                System.Diagnostics.Debug.WriteLine($"BankList Handlebars渲染失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 为Handlebars准备数据（执行原有的showclass逻辑）
        /// </summary>
        private void PrepareDataForHandlebars()
        {
            // 确保action参数被正确设置
            if (string.IsNullOrEmpty(action))
            {
                action = "class"; // 默认为class，这样会调用showclass的默认逻辑
            }
            
            // 执行原有的showclass方法中的数据准备逻辑
            showclass();
        }

        /// <summary>
        /// 构建BankList页面数据模型
        /// </summary>
        /// <returns>BankListPageModel实例</returns>
        private BankListPageModel BuildBankListPageModel()
        {
            var model = new BankListPageModel
            {
                PageTitle = "账目明细",
                IsAdmin = isadmin,
                ShowStats = false
            };

            // 构建站点信息
            model.SiteInfo = new SiteInfoModel
            {
                SiteId = siteid,
                ClassId = classid,
                HttpStart = http_start,
                BackUrl = backurl
            };

            // 构建筛选条件
            model.Filter = BuildFilterModel();

            // 构建账目记录列表
            model.BankLogList = BuildBankLogItemList();

            // 构建分页信息
            model.Pagination = BuildPaginationModel();

            // 构建消息模型
            if (!string.IsNullOrEmpty(ERROR))
            {
                model.Message.HasMessage = true;
                model.Message.Type = "error";
                model.Message.Content = ERROR;
                model.Message.IsSuccess = false;
            }

            // 构建隐藏字段
            model.HiddenFields.Action = "gomod";
            model.HiddenFields.SiteId = siteid;
            model.HiddenFields.ClassId = classid;
            model.HiddenFields.BackUrl = backurl;

            return model;
        }

        /// <summary>
        /// 构建筛选条件模型
        /// </summary>
        /// <returns>FilterModel实例</returns>
        private FilterModel BuildFilterModel()
        {
            var filter = new FilterModel
            {
                ToYear = toyear,
                ToMonth = tomonth,
                Key = key,
                TypeId = typeid,
                TypeKey = typekey
            };

            // 构建年份选项（最近三年）
            var currentYear = DateTime.Now.Year;
            for (int year = currentYear; year >= currentYear - 2; year--)
            {
                filter.YearOptions.Add(new OptionItem
                {
                    Value = year.ToString(),
                    Text = year + "年",
                    Selected = year.ToString() == toyear
                });
            }

            // 构建月份选项
            for (int month = 1; month <= 12; month++)
            {
                filter.MonthOptions.Add(new OptionItem
                {
                    Value = month.ToString(),
                    Text = month + "月",
                    Selected = month.ToString() == tomonth
                });
            }

            // 设置搜索类型选中状态
            foreach (var option in filter.SearchTypeOptions)
            {
                option.Selected = option.Value == typeid;
            }

            return filter;
        }

        /// <summary>
        /// 构建账目记录列表模型
        /// </summary>
        /// <returns>BankLogItemModel列表</returns>
        private List<BankLogItemModel> BuildBankLogItemList()
        {
            var result = new List<BankLogItemModel>();

            // 调试信息
            System.Diagnostics.Debug.WriteLine($"BankList: listVo count = {listVo?.Count ?? 0}, total = {total}");

            if (listVo != null && listVo.Count > 0)
            {
                foreach (var item in listVo)
                {
                    result.Add(new BankLogItemModel
                    {
                        Id = item.id,
                        ActionName = item.actionName ?? "",
                        Money = item.money.ToString(),
                        LeftMoney = item.leftMoney.ToString(),
                        OperaUserId = item.opera_userid.ToString(),
                        OperaNickname = item.opera_nickname ?? "",
                        AddTime = item.addtime,
                        Remark = item.remark ?? ""
                    });
                }
            }

            System.Diagnostics.Debug.WriteLine($"BankList: result count = {result.Count}");
            return result;
        }

        /// <summary>
        /// 构建分页信息模型
        /// </summary>
        /// <returns>PaginationModel实例</returns>
        private PaginationModel BuildPaginationModel()
        {
            var pagination = new PaginationModel
            {
                CurrentPage = (int)CurrentPage,
                TotalPages = (int)Math.Ceiling((double)total / pageSize),
                TotalItems = (int)total,
                PageSize = (int)pageSize,
                HasPages = total > pageSize,
                ShowPagination = total > pageSize
            };

            pagination.IsFirstPage = pagination.CurrentPage <= 1;
            pagination.IsLastPage = pagination.CurrentPage >= pagination.TotalPages;

            return pagination;
        }

        /// <summary>
        /// 使用Handlebars渲染页面
        /// </summary>
        /// <param name="pageModel">页面数据模型</param>
        private void RenderWithHandlebars(BankListPageModel pageModel)
        {
            // 构建头部选项
            var headerOptions = new HeaderOptionsModel
            {
                ShowViewModeToggle = false,
                CustomButtons = new List<HeaderButtonModel>
                {
                    new HeaderButtonModel
                    {
                        Id = "stats",
                        Icon = "bar-chart-3",
                        OnClick = "toggleStats()",
                        Tooltip = "统计信息"
                    }
                }
            };

            // 渲染页面
            string html = TemplateService.RenderPageWithLayout(
                "~/Template/Pages/BankList.hbs",
                pageModel,
                pageModel.PageTitle,
                headerOptions
            );

            // 输出HTML并结束响应
            Response.ContentType = "text/html";
            Response.Write(html);
            Response.End();
        }

        public void showclass()
        {
            key = GetRequestValue("key");
            typeid = GetRequestValue("typeid");
            typekey = GetRequestValue("typekey");
            if (action == "search")
            {
                toyear = GetRequestValue("toyear");
                tomonth = GetRequestValue("tomonth");
            }
            if (action == "mod")
            {
                ShowTipInfo("确定删除吗？<a href=\"" + http_start + "bbs/banklist.aspx?action=gomod&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;toyear=" + toyear + "&amp;tomonth=" + tomonth + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + " \">确定</a>", "bbs/banklist.aspx?action=gomod&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;toyear=" + toyear + "&amp;tomonth=" + tomonth + "&amp;backurl=" + HttpUtility.UrlEncode(backurl));
            }
            else if (action == "gomod")
            {
                string text = DateTime.Now.Year.ToString();
                string text2 = DateTime.Now.Month.ToString();
                MainBll.UpdateSQL("delete from wap_bankLog where siteid=" + siteid + " and  addtime < '" + text + "-" + text2 + "-1 00:00:00'");
            }
            condition = " siteid=" + siteid;
            if (isadmin)
            {
                if (key.Trim() != "" && key.Trim() != "0")
                {
                    condition = condition + " and userid = " + key + " ";
                }
            }
            else
            {
                key = userid;
                condition = condition + " and userid = " + userid + " ";
            }
            if (typekey.Trim() != "" && typeid.Trim() != "")
            {
                if (typeid == "1")
                {
                    condition = condition + " and actionname like '%" + typekey + "%' ";
                }
                else if (typeid == "2")
                {
                    if (!WapTool.IsNumeric(typekey))
                    {
                        typekey = "0";
                    }
                    condition = condition + " and opera_userid  =" + typekey + "  ";
                }
                else if (typeid == "3")
                {
                    condition = condition + " and opera_nickname like '%" + typekey + "%' ";
                }
                else if (typeid == "4")
                {
                    condition = condition + " and remark like '%" + typekey + "%' ";
                }
                else if (typeid == "5")
                {
                    if (!WapTool.IsNumeric(typekey))
                    {
                        typekey = "0";
                    }
                    condition = condition + " and id  =" + typekey + "  ";
                }
            }
            if (WapTool.IsNumeric(toyear))
            {
                condition = condition + " and  Year(addtime) = " + toyear + " ";
            }
            if (WapTool.IsNumeric(tomonth))
            {
                condition = condition + " and  Month(addtime) = " + tomonth + " ";
            }
            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                wap_bankLog_BLL wap_bankLog_BLL = new wap_bankLog_BLL(string_10);
                if (GetRequestValue("getTotal") != "")
                {
                    total = long.Parse(GetRequestValue("getTotal"));
                }
                else
                {
                    total = wap_bankLog_BLL.GetListCount(condition);
                }
                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "bbs/banklist.aspx?action=search&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;typeid=" + HttpUtility.UrlEncode(typeid) + "&amp;typekey=" + HttpUtility.UrlEncode(typekey) + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;toyear=" + toyear + "&amp;tomonth=" + tomonth + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "&amp;getTotal=" + total;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);
                listVo = wap_bankLog_BLL.GetListVo(pageSize, CurrentPage, condition, "*", "id", total, 1L);
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}