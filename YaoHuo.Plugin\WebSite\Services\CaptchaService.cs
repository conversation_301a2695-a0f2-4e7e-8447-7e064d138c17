using System;
using System.Web;
using System.Web.SessionState;
using System.Net;
using KeLin.ClassManager;
using Newtonsoft.Json.Linq;
using TencentCloud.Common;
using TencentCloud.Common.Profile;
using TencentCloud.Captcha.V20190722;
using TencentCloud.Captcha.V20190722.Models;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.WebSite.Services
{
    /// <summary>
    /// 验证码服务类 - 统一处理所有验证码相关逻辑
    /// 支持 GoCaptcha、Cloudflare Turnstile、腾讯云验证码
    /// </summary>
    public static class CaptchaService
    {
        #region 配置属性

        /// <summary>
        /// Cloudflare Turnstile 站点密钥
        /// </summary>
        private static string TurnstileSiteKey => PubConstant.GetAppString("CloudflareTurnstileSiteKey");

        /// <summary>
        /// Cloudflare Turnstile 验证密钥
        /// </summary>
        private static string TurnstileSecretKey => PubConstant.GetAppString("CloudflareTurnstileSecretKey");

        /// <summary>
        /// 当前启用的验证码服务提供商
        /// </summary>
        private static string CaptchaProvider => PubConstant.GetAppString("CaptchaProvider")?.ToLower() ?? "none";

        /// <summary>
        /// 腾讯云验证码 AppId
        /// </summary>
        private static string TencentCloudCaptchaAppId => PubConstant.GetAppString("TencentCloudCaptchaAppId");

        /// <summary>
        /// 腾讯云验证码 AppSecretKey
        /// </summary>
        private static string TencentCloudCaptchaAppSecretKey => PubConstant.GetAppString("TencentCloudCaptchaAppSecretKey");

        /// <summary>
        /// 腾讯云 API SecretId
        /// </summary>
        private static string TencentCloudSecretId => PubConstant.GetAppString("TencentCloudSecretId");

        /// <summary>
        /// 腾讯云 API SecretKey
        /// </summary>
        private static string TencentCloudSecretKey => PubConstant.GetAppString("TencentCloudSecretKey");

        /// <summary>
        /// GoCaptcha 服务启用标志
        /// </summary>
        private static string GoCaptchaEnabled => PubConstant.GetAppString("GoCaptchaEnabled");

        /// <summary>
        /// GoCaptcha 服务地址
        /// </summary>
        private static string GoCaptchaServiceUrl => PubConstant.GetAppString("GoCaptchaServiceUrl");

        #endregion

        #region 公共方法

        /// <summary>
        /// 验证验证码 - 统一入口方法
        /// </summary>
        /// <param name="request">HTTP请求对象</param>
        /// <param name="session">Session对象</param>
        /// <param name="userIP">用户IP地址</param>
        /// <param name="errorMessage">错误信息输出</param>
        /// <returns>验证是否成功</returns>
        public static bool Verify(HttpRequest request, HttpSessionState session, string userIP, out string errorMessage)
        {
            errorMessage = string.Empty;

            // 参数安全检查
            if (request == null)
            {
                errorMessage = "VERIFY_ERROR:请求对象不能为空";
                return false;
            }

            try
            {
                // 获取前端提交的验证码类型
                string captchaType = GetRequestValue(request, "captchaType");

                // 根据 Web.config 中的 CaptchaProvider 配置进行验证
                if (CaptchaProvider == "gocaptcha" && GoCaptchaEnabled == "1" && !string.IsNullOrEmpty(GoCaptchaServiceUrl))
                {
                    // GoCaptcha 是主验证方式，但允许回退到 Cloudflare
                    if (captchaType == "cloudflare")
                    {
                        return VerifyCloudflareTurnstile(request, out errorMessage);
                    }
                    else // 默认使用 GoCaptcha
                    {
                        return VerifyGoCaptcha(request, session, out errorMessage);
                    }
                }
                else if (CaptchaProvider == "cloudflare" && !string.IsNullOrEmpty(TurnstileSiteKey))
                {
                    return VerifyCloudflareTurnstile(request, out errorMessage);
                }
                else if (CaptchaProvider == "tencentcloud" && !string.IsNullOrEmpty(TencentCloudCaptchaAppId) && 
                         !string.IsNullOrEmpty(TencentCloudCaptchaAppSecretKey) && !string.IsNullOrEmpty(TencentCloudSecretId) && 
                         !string.IsNullOrEmpty(TencentCloudSecretKey))
                {
                    return VerifyTencentCloudCaptcha(request, userIP, out errorMessage);
                }
                else
                {
                    // 如果没有配置验证码，或配置为 "none"，则跳过验证
                    return true;
                }
            }
            catch (Exception ex)
            {
                errorMessage = "VERIFY_ERROR:验证过程发生异常 - " + ex.Message;
                return false;
            }
        }

        #endregion

        #region 私有验证方法

        /// <summary>
        /// 验证 GoCaptcha 验证码
        /// </summary>
        /// <param name="request">HTTP请求对象</param>
        /// <param name="session">Session对象</param>
        /// <param name="errorMessage">错误信息输出</param>
        /// <returns>验证是否成功</returns>
        private static bool VerifyGoCaptcha(HttpRequest request, HttpSessionState session, out string errorMessage)
        {
            errorMessage = string.Empty;
            string gocaptchaToken = GetRequestValue(request, "gocaptchaToken");

            if (string.IsNullOrEmpty(gocaptchaToken))
            {
                errorMessage = "NOTHUMANVERIFY_TOKEN_NULL";
                return false;
            }

            try
            {
                if (!GoCaptchaProxy.ValidateAndConsumeToken(gocaptchaToken, session))
                {
                    if (session == null)
                    {
                        errorMessage = "VERIFY_FAILED:系统Session不可用，请刷新页面重试";
                    }
                    else if (gocaptchaToken == "verified")
                    {
                        errorMessage = "VERIFY_FAILED:检测到不安全的验证方式，请重新完成验证码";
                    }
                    else
                    {
                        errorMessage = "VERIFY_FAILED:验证码已失效或无效，请重新验证";
                    }
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = "VERIFY_ERROR:" + ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 验证 Cloudflare Turnstile 验证码
        /// </summary>
        /// <param name="request">HTTP请求对象</param>
        /// <param name="errorMessage">错误信息输出</param>
        /// <returns>验证是否成功</returns>
        private static bool VerifyCloudflareTurnstile(HttpRequest request, out string errorMessage)
        {
            errorMessage = string.Empty;

            if (string.IsNullOrEmpty(TurnstileSiteKey))
            {
                errorMessage = "VERIFY_ERROR:Cloudflare Turnstile 未正确配置";
                return false;
            }

            try
            {
                var token = GetRequestValue(request, "cf-turnstile-response");
                if (string.IsNullOrEmpty(token))
                {
                    errorMessage = "NOTHUMANVERIFY_TOKEN_NULL";
                    return false;
                }

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                var postUrl = "https://challenges.cloudflare.com/turnstile/v0/siteverify";
                var postData = string.Format("secret={0}&response={1}", TurnstileSecretKey, token);
                var data = HttpTool.Post(postUrl, postData);

                if (string.IsNullOrEmpty(data))
                {
                    errorMessage = "NOTHUMANVERIFY_RESPONSE_NULL";
                    return false;
                }

                var jObject = JObject.Parse(data);
                var success = jObject["success"].ToStr().ToBool();

                if (!success)
                {
                    errorMessage = "NOTHUMANVERIFY";
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = "VERIFY_ERROR:" + ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 验证腾讯云验证码
        /// </summary>
        /// <param name="request">HTTP请求对象</param>
        /// <param name="userIP">用户IP地址</param>
        /// <param name="errorMessage">错误信息输出</param>
        /// <returns>验证是否成功</returns>
        private static bool VerifyTencentCloudCaptcha(HttpRequest request, string userIP, out string errorMessage)
        {
            errorMessage = string.Empty;

            try
            {
                var ticket = GetRequestValue(request, "tencent_ticket");
                var randstr = GetRequestValue(request, "tencent_randstr");

                if (string.IsNullOrEmpty(ticket) || string.IsNullOrEmpty(randstr))
                {
                    errorMessage = "NOTHUMANVERIFY_TOKEN_NULL";
                    return false;
                }

                Credential cred = new Credential
                {
                    SecretId = TencentCloudSecretId,
                    SecretKey = TencentCloudSecretKey
                };

                ClientProfile clientProfile = new ClientProfile();
                HttpProfile httpProfile = new HttpProfile();
                httpProfile.Endpoint = ("captcha.tencentcloudapi.com");
                clientProfile.HttpProfile = httpProfile;

                CaptchaClient client = new CaptchaClient(cred, "", clientProfile);
                DescribeCaptchaResultRequest req = new DescribeCaptchaResultRequest();
                req.CaptchaType = 9;
                req.Ticket = ticket;
                req.Randstr = randstr;
                req.CaptchaAppId = ulong.Parse(TencentCloudCaptchaAppId);
                req.AppSecretKey = TencentCloudCaptchaAppSecretKey;
                req.UserIp = userIP ?? string.Empty; // 安全处理空IP

                DescribeCaptchaResultResponse resp = client.DescribeCaptchaResultSync(req);

                if (resp.CaptchaCode != 1)
                {
                    errorMessage = "NOTHUMANVERIFY";
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = "VERIFY_ERROR:" + ex.Message;
                return false;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 安全地获取请求参数值
        /// </summary>
        /// <param name="request">HTTP请求对象</param>
        /// <param name="key">参数键名</param>
        /// <returns>参数值，如果不存在则返回空字符串</returns>
        private static string GetRequestValue(HttpRequest request, string key)
        {
            if (request == null || string.IsNullOrEmpty(key))
            {
                return string.Empty;
            }

            try
            {
                // 优先从Form中获取，然后从QueryString中获取
                string value = request.Form[key];
                if (string.IsNullOrEmpty(value))
                {
                    value = request.QueryString[key];
                }

                return value ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        #endregion
    }
} 