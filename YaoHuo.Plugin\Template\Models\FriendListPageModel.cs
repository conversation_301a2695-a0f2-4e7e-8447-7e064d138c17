using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.Template.Models
{
    /// <summary>
    /// FriendList 页面数据模型
    /// </summary>
    public class FriendListPageModel
    {
        /// <summary>
        /// 页面标题
        /// </summary>
        public string PageTitle { get; set; }

        /// <summary>
        /// 好友类型 (0=好友，1=黑名单，2=追求，4=追求我的人，5=推荐)
        /// </summary>
        public string FriendType { get; set; }

        /// <summary>
        /// 搜索关键字
        /// </summary>
        public string SearchKey { get; set; }

        /// <summary>
        /// 是否显示搜索框 (仅 friendtype=0 时显示)
        /// </summary>
        public bool ShowSearchBox { get; set; }

        /// <summary>
        /// 是否启用黑名单搜索功能（预留给未来使用）
        /// </summary>
        public bool EnableBlacklistSearch { get; set; } = false;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string Error { get; set; }

        /// <summary>
        /// 提示信息
        /// </summary>
        public string Info { get; set; }

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasError => !string.IsNullOrEmpty(Error);

        /// <summary>
        /// 是否有提示信息
        /// </summary>
        public bool HasInfo => !string.IsNullOrEmpty(Info);

        /// <summary>
        /// 好友/黑名单列表
        /// </summary>
        public List<FriendItemModel> FriendsList { get; set; } = new List<FriendItemModel>();

        /// <summary>
        /// 分页导航数据
        /// </summary>
        public PaginationModel Pagination { get; set; }

        /// <summary>
        /// 返回 URL
        /// </summary>
        public string BackUrl { get; set; }

        /// <summary>
        /// 当前用户 ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 站点 ID
        /// </summary>
        public string SiteId { get; set; }

        /// <summary>
        /// 栏目 ID
        /// </summary>
        public string ClassId { get; set; }

        /// <summary>
        /// 是否有操作权限（显示清空按钮等）
        /// </summary>
        public bool HasOperations { get; set; }
    }

    /// <summary>
    /// 单个好友/黑名单项的数据模型
    /// </summary>
    public class FriendItemModel
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 好友用户 ID
        /// </summary>
        public string FriendUserId { get; set; }

        /// <summary>
        /// 好友用户名
        /// </summary>
        public string FriendUserName { get; set; }

        /// <summary>
        /// 好友昵称
        /// </summary>
        public string FriendNickname { get; set; }

        /// <summary>
        /// 好友类型
        /// </summary>
        public int FriendType { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddTime { get; set; }

        /// <summary>
        /// 头像 URL (占位符，暂未实现)
        /// </summary>
        public string AvatarUrl { get; set; } = "";

        /// <summary>
        /// 是否在线 (占位符，暂未实现)
        /// </summary>
        public bool IsOnline { get; set; } = false;

        /// <summary>
        /// 私信链接 (占位符，暂未实现)
        /// </summary>
        public string PrivateMessageUrl { get; set; } = "";

        /// <summary>
        /// 删除链接
        /// </summary>
        public string DeleteUrl { get; set; }

        /// <summary>
        /// 用户详情链接
        /// </summary>
        public string UserDetailUrl { get; set; }
    }
} 