using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.BBS.Models
{
    /// <summary>
    /// 头部选项数据模型
    /// </summary>
    public class HeaderOptionsModel
    {
        /// <summary>
        /// 是否显示视图模式切换按钮 (默认为 true)
        /// </summary>
        public bool ShowViewModeToggle { get; set; } = true;

        /// <summary>
        /// 自定义按钮集合
        /// </summary>
        public List<HeaderButtonModel> CustomButtons { get; set; } = new List<HeaderButtonModel>();

        #region 向后兼容属性 (已废弃，请使用 CustomButtons 集合)

        /// <summary>
        /// 自定义按钮图标 (Lucide 图标名称)
        /// [已废弃] 请使用 CustomButtons 集合
        /// </summary>
        [Obsolete("请使用 CustomButtons 集合替代此属性")]
        public string CustomButtonIcon { get; set; }

        /// <summary>
        /// 自定义按钮链接
        /// [已废弃] 请使用 CustomButtons 集合
        /// </summary>
        [Obsolete("请使用 CustomButtons 集合替代此属性")]
        public string CustomButtonLink { get; set; }

        /// <summary>
        /// 自定义按钮点击事件 (JavaScript)
        /// [已废弃] 请使用 CustomButtons 集合
        /// </summary>
        [Obsolete("请使用 CustomButtons 集合替代此属性")]
        public string CustomButtonOnClick { get; set; }

        #endregion
    }

    /// <summary>
    /// 头部按钮模型
    /// </summary>
    public class HeaderButtonModel
    {
        /// <summary>
        /// 按钮唯一标识符
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 按钮图标 (Lucide 图标名称)
        /// </summary>
        public string Icon { get; set; }

        /// <summary>
        /// 按钮链接地址
        /// </summary>
        public string Link { get; set; }

        /// <summary>
        /// 按钮点击事件 (JavaScript)
        /// </summary>
        public string OnClick { get; set; }

        /// <summary>
        /// 按钮提示文本
        /// </summary>
        public string Tooltip { get; set; }

        /// <summary>
        /// 是否包含下拉菜单
        /// </summary>
        public bool HasDropdown { get; set; }

        /// <summary>
        /// 下拉菜单项集合
        /// </summary>
        public List<HeaderDropdownItemModel> DropdownItems { get; set; } = new List<HeaderDropdownItemModel>();
    }

    /// <summary>
    /// 头部下拉菜单项模型
    /// </summary>
    public class HeaderDropdownItemModel
    {
        /// <summary>
        /// 显示文本
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// 图标 (Lucide 图标名称，可选)
        /// </summary>
        public string Icon { get; set; }

        /// <summary>
        /// 链接地址
        /// </summary>
        public string Link { get; set; }

        /// <summary>
        /// 点击事件 (JavaScript)
        /// </summary>
        public string OnClick { get; set; }

        /// <summary>
        /// 是否为分隔线
        /// </summary>
        public bool IsDivider { get; set; }

        /// <summary>
        /// 是否为当前激活状态
        /// </summary>
        public bool IsActive { get; set; }
    }
}