<div class="fav-type-{{FavTypeId}} pb-4">
{{#if HasError}}
<div class="toast-error" id="errorToast">
    <div class="flex items-center flex-1">
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="x-circle"></i>
        <span class="flex-1 leading-[1.4]">{{Error}}</span>
    </div>
    <button class="bg-transparent border-none text-white cursor-pointer p-1 ml-3 rounded flex items-center justify-center transition-colors duration-200 flex-shrink-0 hover:bg-white/20" onclick="closeToast('errorToast')">
        <i class="w-4 h-4" data-lucide="x"></i>
    </button>
</div>
{{/if}}

{{#if HasInfo}}
<div class="toast-info" id="infoToast">
    <div class="flex items-center flex-1">
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="info"></i>
        <span class="flex-1 leading-[1.4]">{{Info}}</span>
    </div>
    <button class="bg-transparent border-none text-white cursor-pointer p-1 ml-3 rounded flex items-center justify-center transition-colors duration-200 flex-shrink-0 hover:bg-white/20" onclick="closeToast('infoToast')">
        <i class="w-4 h-4" data-lucide="x"></i>
    </button>
</div>
{{/if}}

<div class="card">
    <div class="card-header">
        <div class="card-title justify-between">
            <div class="flex items-center">
                <i class="card-icon" data-lucide="bookmark"></i>
                <span>{{PageTitle}}</span>
            </div>
            {{#if FavoritesList}}
            <span class="text-sm text-text-secondary font-normal inline" id="fav-count-display">共 <span id="fav-count-number">{{Pagination.Total}}</span> 个收藏</span>
            {{/if}}
        </div>
    </div>
    <div class="card-body">
        {{#if ShowSearchBox}}
        <div class="mb-4">
            <form method="post" action="?action=class&siteid={{SiteId}}&favtypeid={{FavTypeId}}&backurl={{BackUrl}}">
                <div class="relative flex items-center">
                    <input type="text" name="key" value="{{SearchKey}}" placeholder="输入标题搜索收藏" class="search-input">
                    <button type="submit" class="search-button">
                        <i data-lucide="search" class="w-5 h-5"></i>
                    </button>
                </div>
            </form>
        </div>
        {{/if}}

        <div class="flex flex-col gap-3 min-h-[100px]">
            {{#if FavoritesList}}
            {{#each FavoritesList}}
            <div class="bg-white rounded-lg border border-border-normal p-4 transition-all duration-200 hover:shadow-md fav-item-card">
                <div class="flex items-center">
                    <div class="flex-1 min-w-0">
                        <div class="text-text-primary font-medium text-base cursor-pointer transition-colors duration-200 hover:text-primary truncate" onclick="location.href='{{FullUrl}}'" {{#if IsExternalLink}}target="_blank"{{/if}}>{{Title}}</div>
                        <div class="flex items-center gap-2 mt-1">
                            <span class="text-text-light text-[13px]">{{FriendlyAddDate}}</span>
                        </div>
                    </div>
                    <div class="flex items-center ml-4">
                        <button class="inline-flex items-center justify-center p-1.5 rounded text-sm font-medium transition cursor-pointer select-none border border-transparent bg-transparent text-text-light hover:bg-border-light hover:text-danger delete-fav-btn" data-delete-url="{{DeleteUrl}}" data-item-title="{{Title}}" aria-label="删除收藏">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            {{/each}}
            {{else}}
            <div class="text-center py-12 px-4 flex flex-col items-center justify-center">
                <div class="mb-4 w-20 h-20">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500" fill="none" class="w-full h-full">
                        <circle cx="250" cy="250" r="200" fill="#F5F5F5"></circle>
                        <path d="M165 220C165 198.402 182.402 181 204 181H296C317.598 181 335 198.402 335 220V280C335 301.598 317.598 319 296 319H204C182.402 319 165 301.598 165 280V220Z" fill="white" stroke="#F59E0B" stroke-width="4"></path>
                        <path d="M200 200L300 200" stroke="#F59E0B" stroke-width="3" stroke-linecap="round"></path>
                        <path d="M200 230L280 230" stroke="#F59E0B" stroke-width="3" stroke-linecap="round"></path>
                        <path d="M200 260L260 260" stroke="#F59E0B" stroke-width="3" stroke-linecap="round"></path>
                        <path d="M220 320L280 320" stroke="#F59E0B" stroke-width="4" stroke-linecap="round"></path>
                    </svg>
                </div>
                <div class="text-lg font-medium text-text-primary mb-1">
                    {{#eq FavTypeId "0"}}还没有收藏任何内容{{else}}该分类下暂无收藏{{/eq}}
                </div>
                <div class="text-sm text-text-secondary">
                    {{#eq FavTypeId "0"}}快去收藏你喜欢的内容吧{{else}}去其他地方看看吧{{/eq}}
                </div>
            </div>
            {{/if}}
        </div>

        {{#if Pagination.ShowPagination}}
        <div class="flex items-center justify-center gap-4 mt-4">
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="prevPageBtn" {{#if Pagination.IsFirstPage}}disabled{{/if}}>
                <i data-lucide="chevron-left" class="w-5 h-5"></i>
            </button>
            <div class="flex-1 text-center text-sm text-text-secondary px-2">
                第 {{Pagination.CurrentPage}} / {{Pagination.TotalPages}} 页
            </div>
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="nextPageBtn" {{#if Pagination.IsLastPage}}disabled{{/if}}>
                <i data-lucide="chevron-right" class="w-5 h-5"></i>
            </button>
        </div>
        {{/if}}
    </div>
</div>

{{!-- 清空收藏按钮 - 放在卡片容器外 --}}
{{#if HasOperations}}
{{#if FavoritesList}}
<div class="mx-4 mt-4">
    <button class="w-full bg-danger text-white border border-danger rounded-md px-4 py-3 font-medium text-sm transition-all duration-200 hover:bg-danger-dark hover:border-danger-dark flex items-center justify-center" id="clear-favorites-btn">
        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
        清空收藏
    </button>
</div>
{{/if}}
{{/if}}
</div>

<script>
// Handlebars 模板中的前端脚本

// Toast 通知处理函数
function closeToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
        toast.classList.add('fade-out');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
}

function autoCloseToast(toastId, delay = 3000) {
    setTimeout(() => {
        closeToast(toastId);
    }, delay);
}

// Toast通知函数
function showToast(type, message) {
    // 创建toast容器
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            pointer-events: none;
        `;
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toast = document.createElement('div');
    const toastId = 'toast-' + Date.now();
    toast.id = toastId;

    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const icon = type === 'success' ? 'check-circle' : 'x-circle';

    toast.style.cssText = `
        background-color: ${type === 'success' ? '#10b981' : '#ef4444'};
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 250px;
        pointer-events: auto;
        transform: translateX(100%);
        transition: transform 0.3s ease-out;
    `;

    toast.innerHTML = `
        <i data-lucide="${icon}" style="width: 16px; height: 16px; flex-shrink: 0;"></i>
        <span style="flex: 1;">${message}</span>
        <button onclick="closeToast('${toastId}')" style="background: none; border: none; color: white; cursor: pointer; padding: 0; margin-left: 8px;">
            <i data-lucide="x" style="width: 14px; height: 14px;"></i>
        </button>
    `;

    toastContainer.appendChild(toast);

    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 显示动画
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 10);

    // 自动关闭
    setTimeout(() => {
        closeToast(toastId);
    }, 4000);
}

// 自定义确认对话框函数
function showCustomConfirm(message, onConfirm) {
    const confirmDialogOverlay = document.createElement('div');
    confirmDialogOverlay.style.cssText = `
        position: fixed; inset: 0; background-color: rgba(0,0,0,0.5);
        display: flex; align-items: center; justify-content: center;
        z-index: 1030;
    `;

    const confirmDialogContent = document.createElement('div');
    confirmDialogContent.style.cssText = `
        background-color: white;
        border-radius: 12px;
        padding: 24px 20px 20px;
        width: 85%; max-width: 360px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        text-align: center;
    `;

    confirmDialogContent.innerHTML = `
        <h3 style="font-size: 18px; font-weight: 600; color: #1f2937; margin-bottom: 12px;">确认操作</h3>
        <p style="color: #6b7280; margin-bottom: 24px; font-size: 15px; line-height: 1.5;">${message}</p>
        <div style="display: flex; justify-content: center; gap: 12px;">
            <button class="custom-confirm-btn custom-confirm-delete" id="confirmCustomConfirm">确定</button>
            <button class="custom-confirm-btn custom-confirm-cancel" id="cancelCustomConfirm">取消</button>
        </div>
    `;

    confirmDialogOverlay.appendChild(confirmDialogContent);
    document.body.appendChild(confirmDialogOverlay);

    // 关闭对话框函数
    function closeDialog() {
        if (document.body.contains(confirmDialogOverlay)) {
            document.body.removeChild(confirmDialogOverlay);
        }
    }

    const cancelBtn = document.getElementById('cancelCustomConfirm');
    if(cancelBtn) cancelBtn.onclick = closeDialog;

    const confirmBtn = document.getElementById('confirmCustomConfirm');
    if(confirmBtn) confirmBtn.onclick = () => {
        onConfirm();
        closeDialog();
    };

    // 点击遮罩关闭
    confirmDialogOverlay.addEventListener('click', function(e) {
        if (e.target === confirmDialogOverlay) {
            closeDialog();
        }
    });
}

// 页面初始化时绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 初始化 Toast 自动关闭
    const errorToast = document.getElementById('errorToast');
    const infoToast = document.getElementById('infoToast');
    
    if (errorToast) {
        autoCloseToast('errorToast', 3000);
    }
    
    if (infoToast) {
        autoCloseToast('infoToast', 3000);
    }

    // 初始化 Lucide 图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 绑定删除收藏按钮事件（Ajax删除）
    document.querySelectorAll('.delete-fav-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const deleteUrl = this.dataset.deleteUrl;
            const itemTitle = this.dataset.itemTitle;
            const itemCard = this.closest('.fav-item-card');

            console.log('删除URL:', deleteUrl);
            console.log('收藏标题:', itemTitle);

            if (deleteUrl) {
                showCustomConfirm(`确定要删除收藏"${itemTitle}"吗？`, function() {
                    // 显示删除中状态
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 animate-spin"></i>';
                    button.disabled = true;

                    // 发送Ajax删除请求
                    console.log('发送删除请求到:', deleteUrl);
                    fetch(deleteUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        }
                    })
                    .then(response => {
                        console.log('删除响应状态:', response.status);
                        console.log('删除响应头:', response.headers.get('content-type'));
                        return response.text(); // 先获取文本，然后尝试解析JSON
                    })
                    .then(responseText => {
                        console.log('删除响应内容:', responseText);
                        try {
                            const data = JSON.parse(responseText);
                            console.log('解析的JSON数据:', data);

                            if (data.success) {
                                // 删除成功，添加淡出动画并移除元素
                                itemCard.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                                itemCard.style.opacity = '0';
                                itemCard.style.transform = 'translateX(20px)';

                                setTimeout(() => {
                                    itemCard.remove();
                                    showToast('success', data.message || '删除成功');

                                    // 检查是否还有收藏项，如果没有则显示空状态
                                    const remainingItems = document.querySelectorAll('.fav-item-card');
                                    if (remainingItems.length === 0) {
                                        location.reload(); // 重新加载页面显示空状态
                                    }
                                }, 300);
                            } else {
                                // 删除失败，恢复按钮状态
                                button.innerHTML = originalText;
                                button.disabled = false;
                                if (typeof lucide !== 'undefined') {
                                    lucide.createIcons();
                                }
                                showToast('error', data.message || '删除失败');
                            }
                        } catch (jsonError) {
                            console.error('JSON解析失败:', jsonError);
                            console.log('原始响应不是JSON格式，可能是HTML页面');
                            // 恢复按钮状态
                            button.innerHTML = originalText;
                            button.disabled = false;
                            if (typeof lucide !== 'undefined') {
                                lucide.createIcons();
                            }
                            showToast('error', '服务器返回了意外的响应格式');
                        }
                    })
                    .catch(error => {
                        console.error('删除请求失败:', error);
                        // 恢复按钮状态
                        button.innerHTML = originalText;
                        button.disabled = false;
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons();
                        }
                        showToast('error', '网络错误，删除失败');
                    });
                });
            }
        });
    });

    // 绑定清空收藏按钮事件（Ajax清空）
    const clearFavoritesBtn = document.getElementById('clear-favorites-btn');
    if (clearFavoritesBtn) {
        clearFavoritesBtn.addEventListener('click', function() {
            showCustomConfirm('确定要清空所有收藏吗？此操作不可恢复！', function() {
                // 显示清空中状态
                const originalText = clearFavoritesBtn.innerHTML;
                clearFavoritesBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>清空中...';
                clearFavoritesBtn.disabled = true;

                // 构建清空收藏的URL
                const currentUrl = new URL(window.location.href);
                const clearUrl = `/bbs/favlist.aspx?action=deleteall&siteid=${currentUrl.searchParams.get('siteid') || ''}&favtypeid=${currentUrl.searchParams.get('favtypeid') || '0'}`;

                // 发送Ajax清空请求
                fetch(clearUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 清空成功，重新加载页面
                        showToast('success', data.message || '清空成功');
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        // 清空失败，恢复按钮状态
                        clearFavoritesBtn.innerHTML = originalText;
                        clearFavoritesBtn.disabled = false;
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons();
                        }
                        showToast('error', data.message || '清空失败');
                    }
                })
                .catch(error => {
                    console.error('清空请求失败:', error);
                    // 恢复按钮状态
                    clearFavoritesBtn.innerHTML = originalText;
                    clearFavoritesBtn.disabled = false;
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                    showToast('error', '网络错误，清空失败');
                });
            });
        });
    }

    // 不再需要下拉菜单相关的全局点击事件

    // 分页按钮事件
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');
    
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', function() {
            if (!this.disabled) {
                const currentUrl = new URL(window.location.href);
                const currentPage = parseInt(currentUrl.searchParams.get('page') || '1');
                if (currentPage > 1) {
                    currentUrl.searchParams.set('page', (currentPage - 1).toString());
                    window.location.href = currentUrl.toString();
                }
            }
        });
    }
    
    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', function() {
            if (!this.disabled) {
                const currentUrl = new URL(window.location.href);
                const currentPage = parseInt(currentUrl.searchParams.get('page') || '1');
                currentUrl.searchParams.set('page', (currentPage + 1).toString());
                window.location.href = currentUrl.toString();
            }
        });
    }
});
</script>
