{{! 搜索框 - 仅当用户ID有效时显示 }}
{{#if SearchForm.CanSearch}}
{{#unless Message.IsError}}
<div class="bg-white p-4 border-b border-gray-100 sticky top-0 z-10">
    <div class="relative flex items-center">
        <form action="{{SearchForm.SearchUrl}}" method="get" class="w-full">
            <input type="hidden" name="action" value="class">
            <input type="hidden" name="siteid" value="{{SiteInfo.SiteId}}">
            <input type="hidden" name="classid" value="{{SiteInfo.ClassId}}">
            <input type="hidden" name="touserid" value="{{Permissions.TargetUserId}}">
            <input type="hidden" name="ot" value="{{Sort.CurrentSort}}">
            
            <div class="relative">
                <!-- 单一容器，搜索框 -->
                <div class="relative">
                    <input type="text"
                           name="searchKey"
                           value="{{SearchForm.SearchKey}}"
                           class="w-full pr-12 py-3 border border-gray-300 rounded-lg text-sm bg-white transition-all duration-200 focus:outline-none focus:border-teal-500 focus:shadow-lg focus:shadow-teal-500/10"
                           style="padding-left: 3.5rem;"
                           placeholder="多个关键词用空格隔开"
                           minlength="1"
                           maxlength="30">
                    
                    <!-- 左侧筛选按钮 -->
                    {{#if Sort.ShowSort}}
                    <div class="absolute left-0 top-0 bottom-0 flex items-center justify-center pl-3 pr-2">
                        <button type="button"
                                class="bg-transparent border-none rounded-full w-8 h-8 flex items-center justify-center text-gray-400 hover:bg-teal-50 hover:text-teal-500 transition-all duration-200 active:scale-90"
                                onclick="toggleSortFilterDropdown()">
                            <i data-lucide="filter" class="w-5 h-5"></i>
                        </button>
                    </div>

                    <!-- 搜索框旁边的筛选下拉菜单 - 移到输入框容器级别 -->
                    <div id="sortFilterDropdown" class="absolute top-full left-0 mt-1 bg-white rounded shadow-md border border-border-normal z-[90] overflow-hidden opacity-0 invisible transition-all w-auto min-w-fit" style="pointer-events: none;">
                        <div class="dropdown-item {{#if Sort.IsNewest}}active{{/if}}" onclick="window.location.href='{{Sort.NewestUrl}}'">
                            <i data-lucide="clock" class="w-4 h-4 mr-2"></i>按最新回复
                        </div>
                        <div class="dropdown-item {{#if Sort.IsOldest}}active{{/if}}" onclick="window.location.href='{{Sort.OldestUrl}}'">
                            <i data-lucide="history" class="w-4 h-4 mr-2"></i>按最早回复
                        </div>
                    </div>
                    {{/if}}
                    
                    <!-- 右侧搜索按钮 -->
                    <div class="absolute right-1 top-1/2 transform -translate-y-1/2 flex items-center">
                    <button type="submit" class="bg-transparent border-none p-2 rounded cursor-pointer flex items-center justify-center text-gray-400 hover:bg-teal-50 hover:text-teal-500 transition-all duration-200 active:scale-90">
                        <i data-lucide="search" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{{/unless}}

{{/if}}





{{! 消息提示 }}
{{#if Message.HasMessage}}
<div class="px-4 py-3">
    <div class="p-4 rounded-lg {{#if Message.IsError}}bg-red-50 text-red-700 border border-red-200{{else}}bg-blue-50 text-blue-700 border border-blue-200{{/if}}">
        {{Message.Content}}
    </div>
</div>
{{/if}}

{{! 管理员操作区域已移至右上角三点菜单 }}

{{! 回复列表 }}
{{#if ReplyList}}
{{#each ReplyList}}
<div class="py-3 {{#if @first}}mt-0.5{{else}}border-t border-gray-100{{/if}} bg-white">
    <div class="flex justify-between items-center mb-2">
        <a href="{{UserInfoUrl}}" class="text-teal-500 font-medium hover:underline">{{Nickname}} <span class="text-sm">({{UserId}})</span></a>
        <div class="text-gray-400 text-sm">#{{Index}}</div>
    </div>
    <div class="mb-2 text-gray-900 reply-content retext">{{{Content}}}</div>
    <div class="flex justify-between items-center">
        <div class="text-gray-400 text-xs flex items-center">
            <i data-lucide="clock" style="width: .65rem; height: .65rem; margin-right: .15rem;"></i>
            {{FormattedDate}}
        </div>
        <div class="text-gray-400">
            <a href="{{ViewUrl}}" class="inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-teal-50 hover:text-teal-500 transition-all duration-200 tooltip-container" data-tooltip="查看帖子">
                <i data-lucide="eye" class="w-4 h-4"></i>
            </a>
        </div>
    </div>
</div>
{{/each}}
{{else}}
<div class="p-8 text-center">
    <i data-lucide="message-circle" class="w-16 h-16 mx-auto text-gray-300 mb-4"></i>
    <p class="text-gray-500">
        {{#if SearchForm.HasSearchResult}}
            没有找到匹配的回复记录
        {{else}}
            暂无回复记录
        {{/if}}
    </p>
    {{#if SearchForm.HasSearchResult}}
    <div class="mt-4">
        <a href="{{SearchForm.SearchUrl}}?action=class&siteid={{SiteInfo.SiteId}}&classid={{SiteInfo.ClassId}}&touserid={{Permissions.TargetUserId}}&ot={{Sort.CurrentSort}}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
            <i data-lucide="list" class="w-4 h-4 mr-2"></i>
            查看全部回复
        </a>
    </div>
    {{/if}}
</div>
{{/if}}

{{! 分页导航 }}
{{#if Pagination.ShowPagination}}
<div class="sticky bottom-0 bg-white border-t border-gray-100 py-4 mt-4">
    <div class="flex items-center justify-between">
        <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="prevPageBtn" {{#if Pagination.IsFirstPage}}disabled{{/if}}>
            <i data-lucide="chevron-left" class="w-5 h-5"></i>
        </button>
        <div class="text-center text-sm text-text-secondary">
            第 {{Pagination.CurrentPage}} / {{Pagination.TotalPages}} 页
        </div>
        <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="nextPageBtn" {{#if Pagination.IsLastPage}}disabled{{/if}}>
            <i data-lucide="chevron-right" class="w-5 h-5"></i>
        </button>
    </div>
</div>
{{/if}}

{{! 不再需要隐藏原始分页的容器 }}

<style>
/* 回复内容样式 - 修复图片和文本对齐问题，兼容脚本处理 */
.reply-content, .retext {
    line-height: 1.6;
}

.reply-content img, .retext img {
    vertical-align: bottom;
    max-width: 100%;
    height: auto;
}

.reply-content .ubbimg, .retext .ubbimg {
    vertical-align: bottom;
    max-width: 100%;
    height: auto;
    display: inline-block;
}

/* 确保内联元素底对齐 */
.reply-content *, .retext * {
    vertical-align: bottom;
}

/* 特殊处理换行标签 */
.reply-content br, .retext br {
    line-height: 1.6;
}

/* 兼容HyperLink.js生成的链接样式 */
.reply-content a, .retext a {
    color: #0d9488;
    text-decoration: none;
}

.reply-content a:hover, .retext a:hover {
    text-decoration: underline;
}

/* Tooltip 样式 */
.tooltip-container {
    position: relative;
}

.tooltip-container::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 4px;
    padding: 6px 8px;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    font-size: 12px;
    font-weight: 500;
    border-radius: 6px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1000;
    pointer-events: none;
}

.tooltip-container::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 2px;
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1000;
    pointer-events: none;
}

.tooltip-container:hover::after,
.tooltip-container:hover::before {
    opacity: 1;
    visibility: visible;
}
</style>

{{!-- 引入必要的脚本 --}}
<script type="text/javascript" src="/NetCSS/JS/HyperLink.js?L15" defer></script>
<script type="text/javascript" src="/NetCSS/JS/BookView/ImageStyle.js" defer></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图标
    lucide.createIcons();

    // 处理分页按钮点击事件
    initializePaginationButtons();

    // 创建并插入选项下拉菜单到按钮容器内
    createOptionsDropdown();
});

// 新版下拉菜单已在Header.hbs中处理，此函数保留用于向后兼容
function createOptionsDropdown() {
    // 新版系统使用HeaderOptions.CustomButtons，无需手动创建下拉菜单
    // 保留此函数以防旧版代码调用
}

// 切换选项下拉菜单显示状态 - 兼容新版Header系统
function toggleOptionsDropdown() {
    // 先关闭其他下拉菜单
    closeSortFilterDropdown();

    // 新版系统：查找admin-options下拉菜单
    const newDropdown = document.getElementById('admin-options-dropdown');
    if (newDropdown) {
        const triggerElement = document.getElementById('admin-options');
        if (typeof toggleHeaderDropdown === 'function' && triggerElement) {
            toggleHeaderDropdown(newDropdown, triggerElement);
        } else {
            // 回退到基本切换
            newDropdown.classList.toggle('show');
        }
        return;
    }

    // 旧版系统：向后兼容
    const oldDropdown = document.getElementById('optionsDropdown');
    if (oldDropdown) {
        oldDropdown.classList.toggle('show');
        if (oldDropdown.classList.contains('show')) {
            oldDropdown.classList.remove('invisible');
            oldDropdown.classList.add('opacity-100', 'pointer-events-auto');
        } else {
            oldDropdown.classList.add('invisible');
            oldDropdown.classList.remove('opacity-100', 'pointer-events-auto');
        }
    }
}

// 关闭选项下拉菜单 - 兼容新版Header系统
function closeOptionsDropdown() {
    // 新版系统：关闭admin-options下拉菜单
    const newDropdown = document.getElementById('admin-options-dropdown');
    if (newDropdown && newDropdown.classList.contains('show')) {
        newDropdown.classList.remove('show');
    }

    // 旧版系统：向后兼容
    const oldDropdown = document.getElementById('optionsDropdown');
    if (oldDropdown && oldDropdown.classList.contains('show')) {
        oldDropdown.classList.remove('show');
        oldDropdown.classList.add('invisible');
        oldDropdown.classList.remove('opacity-100', 'pointer-events-auto');
    }
}

// 关闭筛选下拉菜单
function closeSortFilterDropdown() {
    const dropdown = document.getElementById('sortFilterDropdown');
    if (dropdown && dropdown.classList.contains('show')) {
        dropdown.classList.remove('show');
        dropdown.classList.add('invisible');
        dropdown.classList.remove('opacity-100');
        dropdown.style.pointerEvents = 'none';
    }
}

// 切换搜索框旁边的筛选下拉菜单
function toggleSortFilterDropdown() {
    // 先关闭其他下拉菜单
    closeOptionsDropdown();

    const dropdown = document.getElementById('sortFilterDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
        if (dropdown.classList.contains('show')) {
            dropdown.classList.remove('invisible');
            dropdown.classList.add('opacity-100');
            dropdown.style.pointerEvents = 'auto';
        } else {
            dropdown.classList.add('invisible');
            dropdown.classList.remove('opacity-100');
            dropdown.style.pointerEvents = 'none';
        }
    }
}



// 点击外部关闭下拉菜单
document.addEventListener('click', function(event) {
    // 关闭选项菜单
    const optionsTrigger = event.target.closest('[onclick*="toggleOptionsDropdown"]');
    if (!optionsTrigger) {
        closeOptionsDropdown();
    }

    // 关闭搜索框旁的筛选菜单
    const sortFilterTrigger = event.target.closest('[onclick*="toggleSortFilterDropdown"]');
    if (!sortFilterTrigger) {
        closeSortFilterDropdown();
    }
});

// 管理员清空回复确认 - 使用自定义弹窗
function confirmClearReplies(url) {
    showCustomConfirm('请确认清空操作，此操作不可恢复！<br><br>确定要清空该用户的所有回复吗？', function() {
        window.location.href = url;
    });
}

// 自定义确认对话框函数
function showCustomConfirm(message, onConfirm) {
    const confirmDialogOverlay = document.createElement('div');
    confirmDialogOverlay.style.cssText = `
        position: fixed; inset: 0; background-color: rgba(0,0,0,0.5);
        display: flex; align-items: center; justify-content: center;
        z-index: 1030;
    `;

    const confirmDialogContent = document.createElement('div');
    confirmDialogContent.style.cssText = `
        background-color: white; border-radius: 12px; padding: 24px;
        max-width: 400px; width: 90%; box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1);
    `;

    confirmDialogContent.innerHTML = `
        <h3 style="font-size: 18px; font-weight: 600; color: #1f2937; margin-bottom: 12px;">确认操作</h3>
        <p style="color: #6b7280; margin-bottom: 24px; font-size: 15px; line-height: 1.5;">${message}</p>
        <div style="display: flex; justify-content: center; gap: 12px;">
            <button class="custom-confirm-btn custom-confirm-delete" id="confirmCustomConfirm">确定</button>
            <button class="custom-confirm-btn custom-confirm-cancel" id="cancelCustomConfirm">取消</button>
        </div>
    `;

    confirmDialogOverlay.appendChild(confirmDialogContent);
    document.body.appendChild(confirmDialogOverlay);

    function closeDialog() {
        if (document.body.contains(confirmDialogOverlay)) {
            document.body.removeChild(confirmDialogOverlay);
        }
    }

    const cancelBtn = document.getElementById('cancelCustomConfirm');
    if(cancelBtn) cancelBtn.onclick = closeDialog;

    const confirmBtn = document.getElementById('confirmCustomConfirm');
    if(confirmBtn) confirmBtn.onclick = () => {
        onConfirm();
        closeDialog();
    };

    // 点击遮罩层关闭弹窗
    confirmDialogOverlay.onclick = (e) => {
        if (e.target === confirmDialogOverlay) {
            closeDialog();
        }
    };
}

// 处理分页按钮点击事件
function initializePaginationButtons() {
    // 隐藏旧版分页控件
    const btBox = document.querySelector('.bt-box');
    const showPage = document.querySelector('.showpage');
    if (btBox) {
        btBox.style.display = 'none';
    }
    if (showPage) {
        showPage.style.display = 'none';
    }

    // 为上一页按钮添加点击事件
    const prevBtn = document.getElementById('prevPageBtn');
    if (prevBtn && !prevBtn.disabled) {
        prevBtn.addEventListener('click', function() {
            // 获取当前页码并减一
            const currentPage = {{Pagination.CurrentPage}};
            if (currentPage > 1) {
                navigateToPage(currentPage - 1);
            }
        });
    }

    // 为下一页按钮添加点击事件
    const nextBtn = document.getElementById('nextPageBtn');
    if (nextBtn && !nextBtn.disabled) {
        nextBtn.addEventListener('click', function() {
            // 获取当前页码并加一
            const currentPage = {{Pagination.CurrentPage}};
            const totalPages = {{Pagination.TotalPages}};
            if (currentPage < totalPages) {
                navigateToPage(currentPage + 1);
            }
        });
    }
}



// 跳转到指定页面
function navigateToPage(page) {
    // 构建URL
    const baseUrl = window.location.href.split('?')[0];
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('page', page);
    window.location.href = baseUrl + '?' + urlParams.toString();
}
</script>