using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using KeLin.ClassManager.ExUtility;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.WebSite.Services
{
    /// <summary>
    /// 收藏查询服务类 - 使用参数化查询，支持智能搜索
    /// </summary>
    public class FavQueryService
    {
        private readonly string _connectionString;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        public FavQueryService(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        /// <summary>
        /// 智能搜索收藏列表 - 按标题模糊搜索
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">当前用户ID</param>
        /// <param name="favtypeid">收藏类型ID（0=全部收藏，其他=特定分类）</param>
        /// <param name="searchKey">搜索关键字</param>
        /// <param name="pageSize">每页数量</param>
        /// <param name="currentPage">当前页码</param>
        /// <returns>查询结果</returns>
        public FavQueryResult SearchFavorites(
            string siteid, 
            string userid, 
            string favtypeid = "0", 
            string searchKey = null, 
            int pageSize = 10, 
            int currentPage = 1)
        {
            var result = new FavQueryResult
            {
                Favorites = new List<favdetail_Model>(),
                Total = 0,
                CurrentPage = currentPage,
                PageSize = pageSize
            };

            try
            {
                // 输入验证
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid))
                {
                    System.Diagnostics.Debug.WriteLine("FavQueryService: 参数验证失败");
                    return result;
                }

                // 构建基础查询条件
                var whereClause = "siteid = @siteid AND userid = @userid";
                var parameters = new List<SqlParameter>
                {
                    new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
                    new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) }
                };

                // 处理收藏类型条件
                if (!string.IsNullOrEmpty(favtypeid) && favtypeid != "0")
                {
                    if (WapTool.IsNumeric(favtypeid))
                    {
                        whereClause += " AND favtypeid = @favtypeid";
                        parameters.Add(new SqlParameter("@favtypeid", SqlDbType.BigInt) { Value = long.Parse(favtypeid) });
                        System.Diagnostics.Debug.WriteLine($"FavQueryService: 按收藏类型过滤 - {favtypeid}");
                    }
                }

                // 处理搜索条件
                if (!string.IsNullOrWhiteSpace(searchKey))
                {
                    searchKey = searchKey.Trim();
                    whereClause += " AND title LIKE @searchTitle";
                    parameters.Add(new SqlParameter("@searchTitle", SqlDbType.NVarChar, 255) { Value = $"%{searchKey}%" });
                    System.Diagnostics.Debug.WriteLine($"FavQueryService: 按标题搜索 - {searchKey}");
                }

                // 查询总数
                string countSql = $"SELECT COUNT(id) FROM favdetail WHERE {whereClause}";
                var countResult = DbHelperSQL.ExecuteScalar(_connectionString, CommandType.Text, countSql, parameters.ToArray());
                result.Total = countResult != null ? Convert.ToInt32(countResult) : 0;

                System.Diagnostics.Debug.WriteLine($"FavQueryService: 查询总数 - {result.Total}");

                // 如果没有数据，直接返回
                if (result.Total == 0)
                {
                    return result;
                }

                // 计算分页
                var totalPages = (int)Math.Ceiling((double)result.Total / pageSize);
                currentPage = Math.Max(1, Math.Min(currentPage, totalPages));
                result.CurrentPage = currentPage;
                result.TotalPages = totalPages;

                var offset = (currentPage - 1) * pageSize;

                // 查询数据
                string dataSql = $@"
                    SELECT id, siteid, userid, favtypeid, title, url, adddate
                    FROM favdetail 
                    WHERE {whereClause}
                    ORDER BY id DESC
                    OFFSET @offset ROWS 
                    FETCH NEXT @pageSize ROWS ONLY";

                var dataParameters = new List<SqlParameter>(parameters)
                {
                    new SqlParameter("@offset", SqlDbType.Int) { Value = offset },
                    new SqlParameter("@pageSize", SqlDbType.Int) { Value = pageSize }
                };

                var dataTable = DbHelperSQL.ExecuteDataset(_connectionString, CommandType.Text, dataSql, dataParameters.ToArray()).Tables[0];
                
                foreach (DataRow row in dataTable.Rows)
                {
                    var favorite = new favdetail_Model
                    {
                        id = Convert.ToInt64(row["id"]),
                        siteid = Convert.ToInt64(row["siteid"]),
                        userid = Convert.ToInt64(row["userid"]),
                        favtypeid = Convert.ToInt64(row["favtypeid"]),
                        title = row["title"]?.ToString() ?? "",
                        url = row["url"]?.ToString() ?? "",
                        adddate = Convert.ToDateTime(row["adddate"])
                    };
                    result.Favorites.Add(favorite);
                }

                System.Diagnostics.Debug.WriteLine($"FavQueryService: 查询完成 - 当前页:{currentPage}, 数据量:{result.Favorites.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FavQueryService: 查询异常 - {ex.Message}");
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 验证收藏是否存在
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">用户ID</param>
        /// <param name="favId">收藏ID</param>
        /// <returns>是否存在</returns>
        public bool FavoriteExists(string siteid, string userid, string favId)
        {
            try
            {
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid) || !WapTool.IsNumeric(favId))
                {
                    return false;
                }

                string sql = "SELECT COUNT(id) FROM favdetail WHERE siteid = @siteid AND userid = @userid AND id = @favId";
                
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
                    new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) },
                    new SqlParameter("@favId", SqlDbType.BigInt) { Value = long.Parse(favId) }
                };

                var result = DbHelperSQL.ExecuteScalar(_connectionString, CommandType.Text, sql, parameters);
                return result != null && Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FavQueryService: 验证收藏存在异常 - {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取收藏数量
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">用户ID</param>
        /// <param name="favtypeid">收藏类型ID</param>
        /// <returns>收藏数量</returns>
        public int GetFavoriteCount(string siteid, string userid, string favtypeid = "0")
        {
            try
            {
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid))
                {
                    return 0;
                }

                string sql;
                SqlParameter[] parameters;

                if (string.IsNullOrEmpty(favtypeid) || favtypeid == "0")
                {
                    // 查询全部收藏
                    sql = "SELECT COUNT(id) FROM favdetail WHERE siteid = @siteid AND userid = @userid";
                    parameters = new SqlParameter[]
                    {
                        new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
                        new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) }
                    };
                }
                else
                {
                    // 查询特定类型收藏
                    sql = "SELECT COUNT(id) FROM favdetail WHERE siteid = @siteid AND userid = @userid AND favtypeid = @favtypeid";
                    parameters = new SqlParameter[]
                    {
                        new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
                        new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) },
                        new SqlParameter("@favtypeid", SqlDbType.BigInt) { Value = long.Parse(favtypeid) }
                    };
                }

                var result = DbHelperSQL.ExecuteScalar(_connectionString, CommandType.Text, sql, parameters);
                return result != null ? Convert.ToInt32(result) : 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FavQueryService: 获取收藏数量异常 - {ex.Message}");
                return 0;
            }
        }
    }

    /// <summary>
    /// 收藏查询结果
    /// </summary>
    public class FavQueryResult
    {
        /// <summary>
        /// 收藏列表
        /// </summary>
        public List<favdetail_Model> Favorites { get; set; } = new List<favdetail_Model>();

        /// <summary>
        /// 总数量
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);

        /// <summary>
        /// 是否为第一页
        /// </summary>
        public bool IsFirstPage => CurrentPage <= 1;

        /// <summary>
        /// 是否为最后一页
        /// </summary>
        public bool IsLastPage => CurrentPage >= TotalPages;
    }
}
