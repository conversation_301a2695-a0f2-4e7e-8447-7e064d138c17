<!-- 顶部导航栏 -->
<div class="header">
    <div class="header-content">
        <div class="header-icon" id="back-button">
            <i data-lucide="arrow-left" class="w-6 h-6"></i>
        </div>
        <div class="header-title">{{pageTitle}}</div>
        <div class="header-actions-right">
            {{#if HeaderOptions.ShowViewModeToggle}}
            <div class="header-icon dropdown" id="theme-toggle">
                <i data-lucide="brush" class="w-5 h-5"></i>
                <div class="dropdown-menu" id="skin-dropdown">
                    <div class="dropdown-item active">
                        <span>新版</span>
                    </div>
                    <div class="dropdown-item" onclick="setUiPreferenceCookie('old', 30); window.location.reload();">
                        <span>旧版</span>
                    </div>
                </div>
            </div>
            {{/if}}

            {{!-- 新版：自定义按钮集合 --}}
            {{#each HeaderOptions.CustomButtons}}
            <div class="header-icon{{#if HasDropdown}} dropdown{{/if}}"
                 id="{{Id}}"
                 {{#if OnClick}}onclick="{{OnClick}}"{{else if Link}}onclick="window.location.href='{{Link}}'"{{/if}}
                 {{#if Tooltip}}title="{{Tooltip}}"{{/if}}>
                <i data-lucide="{{Icon}}" class="w-5 h-5"></i>

                {{#if HasDropdown}}
                <div class="dropdown-menu" id="{{Id}}-dropdown">
                    {{#each DropdownItems}}
                    {{#if IsDivider}}
                    <div class="dropdown-divider"></div>
                    {{else}}
                    <div class="dropdown-item{{#if IsActive}} active{{/if}}"
                         {{#if OnClick}}onclick="{{OnClick}}"{{else if Link}}onclick="window.location.href='{{Link}}'"{{/if}}>
                        {{#if Icon}}<i data-lucide="{{Icon}}" class="w-4 h-4 mr-2"></i>{{/if}}
                        <span>{{Text}}</span>
                    </div>
                    {{/if}}
                    {{/each}}
                </div>
                {{/if}}
            </div>
            {{/each}}

            {{!-- 向后兼容：支持旧的单按钮模式 --}}
            {{#if HeaderOptions.CustomButtonIcon}}
            {{#unless HeaderOptions.CustomButtons}}
            <div class="header-icon relative" id="legacy-custom-button"
                 onclick="{{#if HeaderOptions.CustomButtonOnClick}}{{HeaderOptions.CustomButtonOnClick}}{{else}}window.location.href='{{HeaderOptions.CustomButtonLink}}'{{/if}}">
                <i data-lucide="{{HeaderOptions.CustomButtonIcon}}" class="w-5 h-5"></i>
            </div>
            {{/unless}}
            {{/if}}
        </div>
    </div>
</div>

<script>
    // 智能返回按钮功能
    document.addEventListener('DOMContentLoaded', function() {
        const backButton = document.getElementById('back-button');
        
        if (backButton) {
            backButton.addEventListener('click', function() {
                // 简单且兼容性好的解决方案
                smartBack();
            });
        }
    });

    // 智能返回函数
    function smartBack() {
        try {
            const currentUrl = window.location.href;
            const currentPath = window.location.pathname;
            const referrer = document.referrer;

            // 判断当前是否为个人中心页面
            const isMyFilePage = currentPath.includes('myfile.aspx');

            // 如果是个人中心页面，始终跳转到首页
            if (isMyFilePage) {
                window.location.href = '/';
                return;
            }

            // 其他页面的处理逻辑
            // 检查是否有有效的来源页面且不是翻页操作
            if (referrer && referrer !== currentUrl && isValidReferrer(referrer) && !isSamePageNavigation(referrer, currentUrl)) {
                // 尝试返回上一页
                window.history.back();

                // 设置超时检查，如果返回失败则跳转到个人中心
                setTimeout(function() {
                    if (window.location.href === currentUrl) {
                        window.location.href = '/myfile.aspx';
                    }
                }, 500);
            } else {
                // 没有有效来源页面或是翻页操作，直接跳转到个人中心
                window.location.href = '/myfile.aspx';
            }

        } catch (error) {
            // 如果出现任何错误，根据当前页面类型决定跳转目标
            console.log('Back button error:', error);
            const isMyFilePage = window.location.pathname.includes('myfile.aspx');
            if (isMyFilePage) {
                // 个人中心页面出错，跳转到首页
                window.location.href = '/';
            } else {
                // 其他页面出错，跳转到个人中心
                window.location.href = '/myfile.aspx';
            }
        }
    }

    // 检查来源页面是否有效
    function isValidReferrer(referrer) {
        const currentDomain = window.location.origin;
        return referrer.startsWith(currentDomain) || referrer.startsWith('http');
    }

    // 检查是否为同一页面的翻页导航（避免翻页时的返回问题）
    function isSamePageNavigation(referrer, currentUrl) {
        try {
            const referrerUrl = new URL(referrer);
            const currentUrlObj = new URL(currentUrl);

            // 如果路径相同，只是参数不同，认为是同一页面的翻页
            if (referrerUrl.pathname === currentUrlObj.pathname) {
                // 检查是否包含翻页相关参数
                const referrerParams = new URLSearchParams(referrerUrl.search);
                const currentParams = new URLSearchParams(currentUrlObj.search);

                // 如果包含page参数，认为是翻页操作
                if (referrerParams.has('page') || currentParams.has('page')) {
                    return true;
                }

                // 检查其他可能的翻页参数
                const pageParams = ['page', 'lpage', 'CurrentPage'];
                for (const param of pageParams) {
                    if (referrerParams.has(param) || currentParams.has(param)) {
                        return true;
                    }
                }
            }

            return false;
        } catch (error) {
            // URL解析失败，保守处理
            return false;
        }
    }

    // 通用下拉菜单管理系统
    document.addEventListener('DOMContentLoaded', function() {
        initializeHeaderDropdowns();
        initializeThemeToggle();

        // 窗口大小变化时重新定位下拉菜单
        window.addEventListener('resize', function() {
            repositionOpenDropdowns();
        });
    });

    // 初始化所有头部下拉菜单
    function initializeHeaderDropdowns() {
        document.querySelectorAll('.header-actions-right .dropdown').forEach(dropdown => {
            const dropdownMenu = dropdown.querySelector('.dropdown-menu');
            if (dropdownMenu) {
                dropdown.addEventListener('click', function(e) {
                    e.stopPropagation();
                    toggleHeaderDropdown(dropdownMenu, dropdown);
                });
            }
        });

        // 点击外部关闭所有下拉菜单
        document.addEventListener('click', closeAllHeaderDropdowns);
    }

    // 切换指定下拉菜单的显示状态
    function toggleHeaderDropdown(dropdownMenu, triggerElement) {
        const isCurrentlyOpen = dropdownMenu.classList.contains('show');
        closeAllHeaderDropdowns();
        if (!isCurrentlyOpen) {
            dropdownMenu.classList.add('show');
            // 智能定位下拉菜单
            positionHeaderDropdown(dropdownMenu, triggerElement);
        }
    }

    // 智能定位头部下拉菜单，避免溢出
    function positionHeaderDropdown(dropdownMenu, triggerElement) {
        if (!dropdownMenu || !triggerElement) return;

        // 重置样式
        dropdownMenu.style.left = '';
        dropdownMenu.style.right = '';
        dropdownMenu.style.transform = '';

        // 获取触发元素和下拉菜单的尺寸信息
        const triggerRect = triggerElement.getBoundingClientRect();
        const menuRect = dropdownMenu.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const containerPadding = 16; // 容器边距

        // 计算默认位置（居中对齐）
        const defaultLeft = triggerRect.left + (triggerRect.width / 2) - (menuRect.width / 2);

        // 检查是否会溢出右边
        if (defaultLeft + menuRect.width > viewportWidth - containerPadding) {
            // 右对齐到触发元素
            dropdownMenu.style.left = 'auto';
            dropdownMenu.style.right = '0';
            dropdownMenu.style.transform = 'translateX(0)';
        }
        // 检查是否会溢出左边
        else if (defaultLeft < containerPadding) {
            // 左对齐到触发元素
            dropdownMenu.style.left = '0';
            dropdownMenu.style.right = 'auto';
            dropdownMenu.style.transform = 'translateX(0)';
        }
        // 默认居中对齐
        else {
            dropdownMenu.style.left = '50%';
            dropdownMenu.style.right = 'auto';
            dropdownMenu.style.transform = 'translateX(-50%)';
        }
    }

    // 重新定位所有打开的下拉菜单
    function repositionOpenDropdowns() {
        document.querySelectorAll('.header-actions-right .dropdown').forEach(dropdown => {
            const dropdownMenu = dropdown.querySelector('.dropdown-menu');
            if (dropdownMenu && dropdownMenu.classList.contains('show')) {
                positionHeaderDropdown(dropdownMenu, dropdown);
            }
        });
    }

    // 关闭所有头部下拉菜单
    function closeAllHeaderDropdowns() {
        document.querySelectorAll('.header-actions-right .dropdown-menu').forEach(menu => {
            menu.classList.remove('show');
        });

        // 兼容旧版：如果存在全局关闭函数，也调用它
        if (typeof closeAllDropdowns === 'function') {
            closeAllDropdowns();
        }
    }

    // 皮肤切换特殊处理
    function initializeThemeToggle() {
        const activeItem = document.querySelector('#skin-dropdown .dropdown-item.active');
        if (activeItem) {
            activeItem.addEventListener('click', function(e) {
                e.stopPropagation();
                closeAllHeaderDropdowns();
                showToast('当前已是新版界面');
            });
        }
    }

    // 显示提示消息
    function showToast(message) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.className = 'fixed bottom-5 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white py-2.5 px-5 rounded z-[1000]';
        toast.style.opacity = '0';
        toast.style.transition = 'opacity 0.3s ease';
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
</script> 