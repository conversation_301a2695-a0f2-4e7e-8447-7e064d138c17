/* YaoHuo.Plugin/Template/CSS/style.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    body {
        @apply font-sans bg-[#E8E8E8] leading-normal;
    }
}

@layer components {
    /* 基础布局组件 */
    .container {
        @apply max-w-[720px] mx-auto bg-bg-primary min-h-screen;
    }
    
    .main-content {
        @apply pt-16 pb-4;
    }
    
    /* 卡片组件 */
    .card {
        @apply bg-white rounded-md shadow mb-4 overflow-hidden mx-4 mt-4;
    }
    
    .card-header {
        @apply p-4 pb-2 border-b border-border-light;
    }
    
    .card-title {
        @apply text-base font-medium text-text-primary flex items-center;
    }
    
    .card-icon {
        @apply text-primary mr-2;
    }
    
    .card-body {
        @apply p-4;
    }
    
    /* 按钮组件 */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 rounded text-sm font-medium transition cursor-pointer select-none border border-transparent;
    }
    
    .btn-primary {
        @apply bg-primary text-white border-primary hover:bg-primary-dark hover:border-primary-dark;
    }
    
    .btn-outline {
        @apply bg-transparent text-text-tertiary border-border-normal hover:bg-bg-primary hover:border-border-dark hover:text-text-primary;
    }
    
    .btn-destructive {
        @apply bg-danger text-white border-danger hover:bg-danger-dark hover:border-danger-dark;
    }
    
    .btn-ghost {
        @apply bg-transparent text-primary border-none p-1 h-6 hover:bg-primary-alpha-10;
    }
    
    /* 网格布局 */
    .grid-2 {
        @apply grid grid-cols-2 gap-3;
    }
    
    .grid-3 {
        @apply grid grid-cols-3 gap-3;
    }
    
    .grid-4 {
        @apply grid grid-cols-4 gap-3;
    }
    
    /* 统计项组件 */
    .stats-item {
        @apply flex flex-col cursor-pointer py-2 transition-all duration-300 relative overflow-hidden rounded-sm hover:-translate-y-0.5 hover:bg-primary-alpha-05;
    }
    
    .stats-grid {
        @apply grid grid-cols-4 py-3 text-center border-b border-border-light;
    }
    
    /* 好友列表项组件 */
    .friend-item {
        @apply flex items-start p-4 bg-white border border-border-light rounded-md transition-all duration-200 hover:border-primary-alpha-30 relative overflow-visible;
    }
    
    /* Toast 提示组件 */
    .toast-base {
        @apply fixed top-20 left-1/2 transform -translate-x-1/2 z-toast min-w-[300px] max-w-[90%] rounded-xl px-5 py-4 shadow-[0_8px_32px_rgba(0,0,0,0.12)] flex items-center justify-between text-sm font-medium opacity-100 transition-all duration-300 backdrop-blur-sm border border-white/20 text-white;
    }
    
    .toast-success {
        @apply toast-base bg-gradient-to-r from-success to-green-600;
    }
    
    .toast-error {
        @apply toast-base bg-gradient-to-r from-error to-danger;
    }
    
    .toast-warning {
        @apply toast-base bg-gradient-to-r from-warning to-orange-600;
    }
    
    .toast-info {
        @apply toast-base bg-gradient-to-r from-info to-blue-600;
    }
    
    /* 顶部导航栏 */
    .header {
        @apply fixed top-0 w-full max-w-[720px] z-[100] shadow-md text-white;
        background: linear-gradient(135deg, theme('colors.primary') 0%, theme('colors.primary-dark') 100%);
    }
    
    .header-content {
        @apply flex items-center justify-between p-4;
    }
    
    .header-icon {
        @apply w-8 h-8 flex items-center justify-center cursor-pointer rounded transition-colors;
    }
    
    .header-icon:hover {
        @apply bg-white bg-opacity-10;
    }
    
    .header-title {
        @apply text-lg font-medium flex-1 text-center mx-2;
    }
    
    .header-actions-right {
        @apply min-w-8 flex justify-end items-center;
    }
    
    /* 消息提示 */
    .message {
        @apply p-3 rounded-md mb-0 mx-4 text-sm mt-4;
    }
    
    .message.success {
        @apply text-[#15803d];
    }
    
    .message.error {
        @apply bg-bg-error border border-[#fecaca] text-danger;
    }
    
    .message.warning {
        @apply bg-[#fefce8] border border-[#fde047] text-[#a16207];
    }
    
    .message.info {
        @apply bg-bg-info border border-[#7dd3fc] text-[#0369a1];
    }
    
    /* 下拉菜单 */
    .dropdown {
        @apply relative z-dropdown;
    }
    
    .dropdown-menu {
        @apply absolute left-1/2 top-full mt-2 bg-white rounded shadow-md w-auto min-w-fit z-[90] overflow-hidden opacity-0 invisible -translate-x-1/2 -translate-y-2.5 transition-all pointer-events-none;
    }
    
    .dropdown-menu.show {
        @apply opacity-100 visible translate-y-0 pointer-events-auto;
    }
    
    .dropdown-item {
        @apply py-2 px-3 text-sm text-text-tertiary flex items-center justify-start cursor-pointer transition-colors text-left whitespace-nowrap;
    }
    
    .dropdown-item:hover {
        @apply bg-border-light;
    }
    
    .dropdown-item:first-child {
        @apply rounded-t;
    }
    
    .dropdown-item:last-child {
        @apply rounded-b;
    }
    
    .dropdown-item.active {
        @apply bg-primary-alpha-05 text-primary-dark font-medium;
    }

    .dropdown-divider {
        @apply border-t border-border-light my-1;
    }

    /* 纯CSS下拉菜单显示状态 */
    .dropdown-menu.show {
        @apply opacity-100 visible pointer-events-auto;
    }

    /* 头部右侧下拉菜单右对齐，防止溢出 */
    .header-actions-right .dropdown-menu {
        @apply left-auto right-0 translate-x-0;
    }


    
    /* 表单组件 */
    .form-group {
        @apply mb-4;
    }
    
    .form-group:last-child {
        @apply mb-0;
    }
    
    .form-label {
        @apply block text-sm font-medium text-text-secondary mb-2;
    }
    
    .form-label.required::after {
        @apply text-danger;
        content: " *";
    }
    
    .form-input {
        @apply w-full border border-border-normal rounded text-base bg-white transition-colors outline-none h-11 min-h-11 leading-normal box-border flex items-center px-3 py-0;
        -webkit-appearance: none;
        appearance: none;
    }
    
    .form-input:focus {
        @apply border-primary;
        box-shadow: 0 0 0 2px theme('colors.primary-alpha-20');
    }
    
    .form-input::placeholder {
        @apply text-text-light;
    }
    
    .form-input:disabled {
        @apply bg-bg-gray-50 cursor-not-allowed opacity-60;
    }
    
    .form-input.error {
        @apply border-danger;
    }
    
    .form-input.error:focus {
        @apply border-danger;
        box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
    }
    
    /* 表单选择器 */
    .form-select {
        @apply w-full border border-border-normal rounded text-base bg-white transition-colors outline-none cursor-pointer h-11 min-h-11 leading-normal box-border flex items-center pl-3 pr-10 py-0;
        -webkit-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%236b7280' d='M6 8.5L2.5 5h7L6 8.5z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 12px 12px;
    }
    
    .form-select:focus {
        @apply border-primary;
        box-shadow: 0 0 0 2px theme('colors.primary-alpha-20');
    }
    
    .form-select:disabled {
        @apply bg-bg-gray-50 cursor-not-allowed opacity-60;
    }
    
    /* 表单提示和错误 */
    .form-hint {
        @apply text-xs text-text-secondary mt-1;
    }
    
    .form-error {
        @apply text-xs text-danger mt-1;
    }
    
    /* 搜索框组件 */
    .search-input {
        @apply w-full py-2.5 pr-10 pl-3 border border-border-normal rounded-md text-sm bg-white transition-all duration-200 focus:outline-none focus:border-primary focus:shadow-[0_0_0_3px_rgba(88,180,176,0.1)] placeholder:text-text-light;
    }
    
    .search-button {
        @apply absolute right-1 top-1/2 transform -translate-y-1/2 bg-transparent border-none p-2 rounded-md cursor-pointer flex items-center justify-center text-text-light transition-all duration-200 hover:bg-primary-alpha-10 hover:text-primary;
    }
    
    /* FriendList 页面特定样式 */
    
    /* 好友类型图标颜色覆盖 */
    .friend-type-0 .text-primary {
        color: theme('colors.primary'); /* 好友 - 青色 */
    }
    
    .friend-type-1 .text-primary {
        color: theme('colors.danger'); /* 黑名单 - 红色 */
    }
    
    .friend-type-2 .text-primary,
    .friend-type-4 .text-primary {
        color: theme('colors.warning'); /* 追求相关 - 橙色 */
    }
    
    .friend-type-5 .text-primary {
        color: theme('colors.violet.600'); /* 推荐 - 紫色 */
    }
    
    /* 下拉菜单智能定位 */
    .dropdown-menu-smart {
        /* 默认向下显示，缩短距离 */
        top: 100%;
        bottom: auto;
        margin-top: 0.125rem;
        margin-bottom: 0;
    }
    
    /* 当好友列表容器中的最后一个条目时，下拉菜单向上显示 */
    .friend-item:last-child .dropdown-menu-smart {
        top: auto;
        bottom: 100%;
        margin-top: 0;
        margin-bottom: 0.125rem;
    }
    
    /* Toast 淡出动画 */
    .fade-out {
        @apply opacity-0 -translate-y-2.5;
    }
    
    /* 自定义确认对话框按钮样式 */
    .custom-confirm-btn {
        @apply border-none rounded-md text-base font-medium cursor-pointer transition-all duration-200 h-11 flex items-center justify-center w-[45%];
    }
    
    .custom-confirm-cancel {
        @apply bg-bg-gray-100 text-text-secondary hover:bg-border-normal hover:text-text-primary;
    }
    
    .custom-confirm-delete {
        @apply bg-danger text-white hover:bg-danger-dark;
    }
    
    /* 表单行布局 */
    .form-row {
        @apply flex gap-3;
    }
    
    .form-row .form-group {
        @apply flex-1;
    }
    
    /* 展开更多功能 */
    .expand-toggle {
        @apply text-center mt-3 mb-2 pt-2 border-t border-border-light;
    }
    
    .expand-btn {
        @apply text-sm text-text-secondary bg-transparent border-none p-2 rounded-md transition-all cursor-pointer inline-flex items-center justify-center hover:bg-primary-alpha-05 hover:text-primary;
    }
    
    .expand-btn .icon {
        @apply transition-transform duration-200;
    }
    
    .expand-btn.expanded .icon {
        @apply rotate-180;
    }
    
    .more-fields {
        @apply mt-3 pt-2 overflow-hidden transition-all duration-200 ease-in-out;
    }
    
    .more-fields.expanding {
        animation: expandFields 0.2s ease-in-out;
    }
    
    .more-fields.collapsing {
        animation: collapseFields 0.2s ease-in-out;
    }
    
    /* 表单操作区域 */
    .form-actions {
        @apply mt-4 px-4 mb-2;
    }
    
    .form-submit {
        @apply w-full py-4 px-4 text-base font-medium max-w-[400px] mx-auto flex items-center justify-center gap-2 bg-gradient-to-br from-primary to-primary-dark text-white rounded shadow-md transition-all hover:-translate-y-0.5 hover:shadow-lg active:translate-y-0 active:shadow-sm;
    }
    
    .form-submit .icon {
        @apply w-5 h-5 flex-shrink-0;
    }
    
    /* 图标尺寸 */
    .icon {
        @apply w-4 h-4;
    }
    
    /* 个人资料编辑按钮 */
    .edit-profile {
        @apply bg-white bg-opacity-20 rounded-md py-2 px-3 cursor-pointer flex items-center transition-colors select-none relative z-50;
    }
    
    .edit-profile:hover {
        @apply bg-white bg-opacity-30;
    }
    
    /* 勋章图片样式 */
    .card-body img {
        @apply inline-block;
    }
    
    @media (max-width: 768px) {
        .container {
            @apply max-w-full;
        }

        .header {
            @apply max-w-full;
        }

        .card {
            @apply mx-3 mb-3 mt-3;
        }

        .card-header {
            @apply p-3 pb-2;
        }

        .card-body {
            @apply p-3;
        }

        .form-actions {
            @apply px-3;
        }

        .form-submit {
            @apply max-w-none;
        }
    }

    /* 400px 以下屏幕进一步缩小左右 margin */
    @media (max-width: 400px) {
        .card {
            @apply mx-2 mb-3 mt-3;
        }

        .card-header {
            @apply p-2 pb-1;
        }

        .card-body {
            @apply p-2;
        }

        .form-actions {
            @apply px-2;
        }

        /* 表格小屏幕优化 */
        .table-responsive th,
        .table-responsive td {
            @apply py-2 px-1 text-center align-middle;
        }

        /* 项目列文本不换行 */
        .table-responsive td:first-child div {
            white-space: nowrap;
            font-size: 0.8rem;
        }

        /* 金额和余额列数字不换行 */
        .table-responsive td:nth-child(2) span,
        .table-responsive td:nth-child(3) span {
            white-space: nowrap;
            font-size: 0.8rem;
        }

        /* 时间列保持当前换行方式 */
        .table-responsive td:nth-child(5) span {
            white-space: nowrap;
            font-size: 0.8rem;
        }
    }
    
    /* 消息组件 */
    .message-error {
        @apply bg-bg-error text-danger-dark;
    }
    
    .message-warning {
        @apply bg-yellow-100 text-warning;
    }
    
    .message-success {
        @apply bg-green-100 text-success;
    }
    
    .message-info {
        @apply bg-bg-info text-info;
    }
    
    /* 自定义绿色圆点列表标记 */
    .custom-bullet {
        @apply relative pl-4;
    }
    
    .custom-bullet::before {
        content: '●';
        color: theme('colors.primary');
        margin-right: 0.5rem;
    }

    /* 移动端Safari特殊处理 */
    @media screen and (-webkit-min-device-pixel-ratio: 0) {
        .form-input,
        .form-select {
            -webkit-appearance: none;
            border-radius: 0.375rem;
        }
    }
    
    /* 表格响应式组件 */
    .table-responsive {
        @apply overflow-x-auto;
    }
    
    /* 表格移动端样式 */
    @media (max-width: 768px) {
        .table-responsive {
            overflow-x: auto;
        }
        
        .table-responsive table {
            @apply w-full;
            table-layout: fixed;
        }
        
        .table-responsive th,
        .table-responsive td {
            @apply break-words whitespace-normal align-middle py-2 px-2 text-center;
        }
        
        /* 移除固定列宽，允许内容自动适应 */
    }
} 

/* 个人中心弹窗精细化响应式调整 - 在 @layer 外部防止被 Tailwind purge 移除 */

/* 390px 以下屏幕优化 - 对应旧版第一级优化 */
@media (max-width: 390px) {
    /* 论坛互动网格容器 */
    #yaojing-rule-modal .grid.grid-cols-3 {
        gap: 6px;
    }
    
    /* 论坛互动卡片项（派币帖、悬赏帖、小游戏） */
    #yaojing-rule-modal .grid.grid-cols-3 > div {
        padding: 6px 8px;
        font-size: 13px;
    }
    
    /* 图标间距调整 */
    #yaojing-rule-modal .grid.grid-cols-3 > div i {
        margin-right: 4px;
    }
}

/* 350px 以下屏幕进一步优化 - 对应旧版第二级优化 (已从 345px 调整) */
@media (max-width: 350px) {
    /* 模态框内容内边距优化 */
    #yaojing-rule-modal > div,
    #level-rule-modal > div,
    #time-rule-modal > div {
        padding: 16px;
    }
    
    /* 论坛互动网格容器 */
    #yaojing-rule-modal .grid.grid-cols-3 {
        gap: 4px;
    }
    
    /* 论坛互动卡片项 */
    #yaojing-rule-modal .grid.grid-cols-3 > div {
        padding: 4px 6px;
        font-size: 12px;
    }
    
    /* 图标间距调整 */
    #yaojing-rule-modal .grid.grid-cols-3 > div i {
        margin-right: 2px;
    }
}

/* 310px 以下屏幕（极小屏幕）优化 - 对应旧版第三级优化 */
@media (max-width: 310px) {
    /* 所有弹窗的内容区域内边距优化 */
    #yaojing-rule-modal > div,
    #level-rule-modal > div,
    #time-rule-modal > div {
        padding: 12px;
    }
    
    /* 论坛互动网格容器 */
    #yaojing-rule-modal .grid.grid-cols-3 {
        gap: 2px;
    }
    
    /* 论坛互动卡片项 */
    #yaojing-rule-modal .grid.grid-cols-3 > div {
        padding: 2px 4px;
        font-size: 11px;
    }
    
    /* 图标间距调整 */
    #yaojing-rule-modal .grid.grid-cols-3 > div i {
        margin-right: 2px;
    }
} 