﻿using System;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.WebSite;
using System.Collections.Generic;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_modfile_del : MyPageWap
    {
        private readonly string _instanceName = PubConstant.GetAppString("InstanceName");
        public string formToken = "";
        public bool hasPermission = false;
        public bool isAuthor = false;

        public string action = "";
        public string id = "";
        public string lpage = "";
        public string INFO = "";
        public string ERROR = "";
        public string string_12 = "";
        public string delid = "";
        public wap_bbs_Model bbsVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 1. 获取参数
                action = GetRequestValue("action");
                id = GetRequestValue("id");
                delid = GetRequestValue("delid");
                lpage = GetRequestValue("lpage");
                string_12 = GetRequestValue("sub");

                // 2. 基础验证
                if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
                {
                    ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
                    return;
                }

                // 3. 验证帖子ID和附件ID
                if (string.IsNullOrEmpty(id))
                {
                    ShowTipInfo("参数无效！", "");
                    return;
                }

                // 如果不是删除全部，则需要验证delid
                if (action != "godelall" && string.IsNullOrEmpty(delid))
                {
                    ShowTipInfo("参数无效！", "");
                    return;
                }

                // 4. 获取帖子数据
                wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(_instanceName);
                bbsVo = wap_bbs_BLL.GetModel(long.Parse(id));
                if (bbsVo == null)
                {
                    ShowTipInfo("已删除！或不存在！", "");
                    return;
                }

                // 5. 检查帖子状态
                if (bbsVo.ischeck == 1L)
                {
                    ShowTipInfo("正在审核中！", "");
                    return;
                }
                if (bbsVo.book_classid.ToString() != classid)
                {
                    ShowTipInfo("栏目ID对不上！可能没有传classid值！", "");
                    return;
                }
                if (bbsVo.islock == 1L)
                {
                    ShowTipInfo("此贴已锁！", "bbs/book_view_admin.aspx?siteid=" + siteid + "&amp;classid=" + bbsVo.book_classid + "&amp;id=" + bbsVo.id + "&amp;lpage=" + lpage);
                    return;
                }

                // 6. 权限验证
                if (userid == bbsVo.book_pub.ToString())
                {
                    hasPermission = true;
                    isAuthor = true;
                }
                else if (CheckManagerLvl("04", classVo.adminusername))
                {
                    hasPermission = true;
                }

                if (!hasPermission)
                {
                    ShowTipInfo("您没有权限删除此附件！", "");
                    return;
                }

                // 7. 处理删除操作
                if (action == "godel" || action == "godelall")
                {
                    string token = Request["token"];
                    string tokenKey = action == "godel" ? "formTokenList_modfile_del_" + id + "_" + delid : "formTokenList_modfile_del_" + id;
                    if (!ValidateFormToken(tokenKey, token))
                    {
                        ShowTipInfo("安全验证失败，请刷新页面重试", "");
                        return;
                    }

                    try
                    {
                        wap2_attachment_BLL wap2_attachment_BLL = new wap2_attachment_BLL(_instanceName);

                        if (action == "godelall")
                        {
                            List<wap2_attachment_Model> attachList = wap2_attachment_BLL.GetListVo(" book_type='bbs' and book_id=" + id);

                            if (attachList != null && attachList.Count > 0)
                            {
                                foreach (wap2_attachment_Model attach in attachList)
                                {
                                    if (attach.siteid.ToString() != siteid)
                                    {
                                        continue;
                                    }
                                    
                                    // 权限验证：必须是附件上传者或管理员
                                    if (userid != attach.userid.ToString())
                                    {
                                        if (!CheckManagerLvl("04", classVo.adminusername))
                                        {
                                            ShowTipInfo("您没有权限删除附件ID:" + attach.ID + "，请联系管理员或附件上传者！", "");
                                            return;
                                        }
                                    }

                                    DeleteFile("bbs", attach.book_file, GetUrlQueryString().Replace("godelall", "go"));
                                    wap2_attachment_BLL.Delete(attach.ID);
                                }

                                string text = "{" + userVo.nickname + "(ID" + userVo.userid + ")批量删除附件" + string.Format("{0:MM-dd HH:mm}", DateTime.Now) + "}<br/>";
                                bbsVo.whylock = text + bbsVo.whylock;
                                wap_bbs_BLL.Update(bbsVo);
                            }
                            INFO = "OK";
                        }
                        else
                        {
                            wap2_attachment_Model wap2_attachment_Model = wap2_attachment_BLL.GetModel(long.Parse(delid));
                            if (wap2_attachment_Model == null)
                            {
                                ShowTipInfo("已删除！或不存在！", "");
                                return;
                            }
                            if (wap2_attachment_Model.siteid.ToString() != siteid)
                            {
                                base.Response.End();
                            }

                            // 重要：验证附件是否属于当前帖子
                            if (wap2_attachment_Model.book_type != "bbs" || wap2_attachment_Model.book_id.ToString() != id)
                            {
                                ShowTipInfo("附件不属于当前帖子，无法删除！", "");
                                return;
                            }

                            // 权限验证：必须是附件上传者或管理员
                            if (userid != wap2_attachment_Model.userid.ToString())
                            {
                                if (!CheckManagerLvl("04", classVo.adminusername))
                                {
                                    ShowTipInfo("您没有权限删除此附件！", "");
                                    return;
                                }
                            }

                            DeleteFile("bbs", wap2_attachment_Model.book_file, GetUrlQueryString().Replace("godel", "go"));
                            wap2_attachment_BLL.Delete(long.Parse(delid));
                            string text = "{" + userVo.nickname + "(ID" + userVo.userid + ")删除附件" + string.Format("{0:MM-dd HH:mm}", DateTime.Now) + "}<br/>";
                            bbsVo.whylock = text + bbsVo.whylock;
                            wap_bbs_BLL.Update(bbsVo);
                            INFO = "OK";
                        }

                        Session["formToken"] = null;
                        ClearFormToken(tokenKey);
                    }
                    catch (Exception ex)
                    {
                        ERROR = ex.ToString();
                    }
                }
                else
                {
                    string tokenKey = "formTokenList_modfile_del_" + id + "_" + delid;
                    formToken = GenerateFormToken(tokenKey);
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}